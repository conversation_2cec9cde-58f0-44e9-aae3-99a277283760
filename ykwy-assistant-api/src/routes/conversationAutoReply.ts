import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';
import { notifyAutoReplyStatusChanged } from '../services/eventService';
import { salesAgentIntegrationService } from '../services/salesAgentIntegrationService';

const conversationAutoReplyRoute = new Hono();

// 获取对话自动回复状态
conversationAutoReplyRoute.get(
  '/:conversationId/auto-reply',
  zValidator(
    'param',
    z.object({
      conversationId: z.string().min(1, '对话ID不能为空'),
    }),
  ),
  async (c) => {
    try {
      const { conversationId } = c.req.valid('param');

      // 检查UUID格式避免Prisma验证错误，然后查找conversation
      let actualConversationId = conversationId;
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(conversationId);

      let conversation = null;
      if (isUUID) {
        // 如果是UUID格式，通过ID查找
        conversation = await prisma.conversation.findUnique({
          where: { id: conversationId },
          select: {
            id: true,
            autoReplyEnabled: true,
            organizationId: true,
            conversationCode: true,
          },
        });
      } else {
        // 如果不是UUID格式，通过conversationCode查找
        // 首先尝试精确匹配
        conversation = await prisma.conversation.findFirst({
          where: {
            conversationCode: conversationId, // 精确匹配
            status: { not: 'CLOSED' },
          },
          select: {
            id: true,
            autoReplyEnabled: true,
            organizationId: true,
            conversationCode: true,
          },
        });

        // 如果精确匹配没找到，尝试前缀匹配（兼容性处理）
        if (!conversation) {
          conversation = await prisma.conversation.findFirst({
            where: {
              conversationCode: { startsWith: `${conversationId}#` }, // conversationCode + '#' 开头的匹配
              status: { not: 'CLOSED' },
            },
            select: {
              id: true,
              autoReplyEnabled: true,
              organizationId: true,
              conversationCode: true,
            },
          });

          // 如果找到前缀匹配，更新conversationCode为标准格式
          if (conversation) {
            await prisma.conversation.update({
              where: { id: conversation.id },
              data: {
                conversationCode: conversationId,
                lastActivity: new Date(),
              },
            });
          }
        }

        // 兼容性处理：如果conversationCode字段中没找到，尝试title字段（旧数据）
        if (!conversation) {
          conversation = await prisma.conversation.findFirst({
            where: {
              title: conversationId, // 精确匹配旧格式
              status: { not: 'CLOSED' },
            },
            select: {
              id: true,
              autoReplyEnabled: true,
              organizationId: true,
              conversationCode: true,
            },
          });

          // 如果在title中找到，迁移到conversationCode字段
          if (conversation) {
            await prisma.conversation.update({
              where: { id: conversation.id },
              data: {
                conversationCode: conversationId,
                title: null, // 清理旧字段
                lastActivity: new Date(),
              },
            });
          }
        }
      }

      if (!conversation) {
        return c.json(createErrorResponse('对话不存在'), 404);
      }

      actualConversationId = conversation.id;

      return c.json(
        createResponse(
          {
            conversationId: actualConversationId,
            autoReplyEnabled: conversation.autoReplyEnabled,
          },
          '获取自动回复状态成功',
        ),
      );
    } catch (error) {
      return handleError(c, error);
    }
  },
);

/**
 * 设置对话自动回复状态
 * 支持conversationCode和UUID两种格式
 * 当使用conversationCode时，会批量更新所有相同conversationCode的对话实例
 * - 当启用自动回复时，系统会处理用户最新消息并通知客服人员
 * - 当关闭自动回复时，系统会通知客服人员接管会话
 */
conversationAutoReplyRoute.put(
  '/:conversationId/auto-reply',
  zValidator(
    'param',
    z.object({
      conversationId: z.string().min(1, '对话ID不能为空'),
    }),
  ),
  zValidator(
    'json',
    z.object({
      enabled: z.boolean(),
    }),
  ),
  async (c) => {
    try {
      const { conversationId } = c.req.valid('param');
      const { enabled } = c.req.valid('json');

      // 检查UUID格式避免Prisma验证错误，然后查找conversation
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(conversationId);

      let targetConversations = [];
      let conversationCode = null;

      if (isUUID) {
        // 如果是UUID格式，只更新单个对话
        const conversation = await prisma.conversation.findUnique({
          where: { id: conversationId },
          select: {
            id: true,
            organizationId: true,
            autoReplyEnabled: true,
            customerId: true,
            conversationCode: true,
          },
        });

        if (!conversation) {
          return c.json(createErrorResponse('对话不存在'), 404);
        }

        targetConversations = [conversation];
        conversationCode = conversation.conversationCode;
      } else {
        // 如果不是UUID格式，查找所有相同conversationCode的对话
        conversationCode = conversationId;

        // 首先尝试精确匹配
        targetConversations = await prisma.conversation.findMany({
          where: {
            conversationCode: conversationCode, // 精确匹配
            status: { not: 'CLOSED' },
          },
          select: {
            id: true,
            organizationId: true,
            autoReplyEnabled: true,
            customerId: true,
            conversationCode: true,
          },
        });

        // 如果精确匹配没找到，尝试前缀匹配（兼容性处理）
        if (targetConversations.length === 0) {
          targetConversations = await prisma.conversation.findMany({
            where: {
              conversationCode: { startsWith: `${conversationCode}#` }, // conversationCode + '#' 开头的匹配
              status: { not: 'CLOSED' },
            },
            select: {
              id: true,
              organizationId: true,
              autoReplyEnabled: true,
              customerId: true,
              conversationCode: true,
            },
          });

          // 如果找到前缀匹配，更新conversationCode为标准格式
          if (targetConversations.length > 0) {
            await prisma.conversation.updateMany({
              where: {
                id: { in: targetConversations.map((c) => c.id) },
              },
              data: {
                conversationCode: conversationCode,
                lastActivity: new Date(),
              },
            });
          }
        }

        // 兼容性处理：如果conversationCode字段中没找到，尝试title字段（旧数据）
        if (targetConversations.length === 0) {
          targetConversations = await prisma.conversation.findMany({
            where: {
              title: conversationCode, // 精确匹配旧格式
              status: { not: 'CLOSED' },
            },
            select: {
              id: true,
              organizationId: true,
              autoReplyEnabled: true,
              customerId: true,
              conversationCode: true,
            },
          });

          // 如果在title中找到，迁移到conversationCode字段
          if (targetConversations.length > 0) {
            await prisma.conversation.updateMany({
              where: {
                id: { in: targetConversations.map((c) => c.id) },
              },
              data: {
                conversationCode: conversationCode,
                title: null, // 清理旧字段
                lastActivity: new Date(),
              },
            });
          }
        }

        if (targetConversations.length === 0) {
          return c.json(createErrorResponse('对话不存在'), 404);
        }
      }

      // 获取需要更新的对话ID列表
      const conversationIds = targetConversations.map((c) => c.id);

      // 更新自动回复状态
      console.log(`[Auto Reply] Attempting to update ${conversationIds.length} conversations with enabled=${enabled}`);
      console.log(`[Auto Reply] Target conversations:`, conversationIds);

      // 批量更新所有目标对话
      const updateResult = await prisma.conversation.updateMany({
        where: {
          id: { in: conversationIds },
        },
        data: { autoReplyEnabled: enabled },
      });

      console.log(`[Auto Reply] Batch update result: ${updateResult.count} conversations updated`);

      // 检查哪些对话的状态发生了变化
      const changedConversations = targetConversations.filter((conv) => conv.autoReplyEnabled !== enabled);
      const hasChanges = changedConversations.length > 0;

      console.log(`[Auto Reply] Found ${changedConversations.length} conversations with status change`);

      // 如果自动回复状态发生变化，无论是开启还是关闭，都发送通知
      if (hasChanges) {
        console.log(`[Auto Reply] Auto reply status changed, sending notifications...`);

        // 异步发送WebSocket通知给所有状态发生变化的对话
        Promise.all(
          changedConversations.map(async (conversation) => {
            try {
              // 跳过没有organizationId的会话
              if (!conversation.organizationId) {
                console.log(`[Auto Reply] Skipping conversation ${conversation.id} without organizationId`);
                return;
              }

              // 获取客户信息
              const customer = await prisma.customer.findUnique({
                where: { id: conversation.customerId },
                select: { nickname: true },
              });

              // 发送WebSocket通知
              if (customer) {
                notifyAutoReplyStatusChanged(conversation.organizationId, conversation.id, customer.nickname, enabled);
                console.log(`[Auto Reply] Notification sent for conversation ${conversation.id}, customer: ${customer.nickname}, auto reply ${enabled ? 'enabled' : 'disabled'}`);
              }
            } catch (error) {
              console.error(`[Auto Reply] Error sending notification for conversation ${conversation.id}:`, error);
            }
          }),
        ).catch((error) => {
          console.error(`[Auto Reply] Error processing notifications:`, error);
        });
      }

      // 如果是开启自动回复，检查是否需要立即回复最新的客户消息
      if (enabled && hasChanges) {
        console.log(`[Auto Reply] Auto reply enabled, checking for pending customer messages...`);

        // 为每个状态发生变化的对话处理最新消息
        Promise.all(
          changedConversations.map(async (conversation) => {
            try {
              // 获取最新一条消息
              const latestMessage = await prisma.message.findFirst({
                where: { conversationId: conversation.id },
                orderBy: { createdAt: 'desc' },
              });

              // 如果最新消息是客户消息，立即触发自动回复
              if (latestMessage && latestMessage.senderType === 'CUSTOMER') {
                console.log(`[Auto Reply] Found pending customer message in conversation ${conversation.id}, triggering immediate auto reply...`);
                await salesAgentIntegrationService.handleNewMessage(latestMessage);
              } else {
                console.log(`[Auto Reply] No pending customer messages found in conversation ${conversation.id}`);
              }
            } catch (error) {
              console.error(`[Auto Reply] Error processing immediate auto reply for conversation ${conversation.id}:`, error);
            }
          }),
        ).catch((error) => {
          console.error(`[Auto Reply] Error processing immediate auto replies:`, error);
        });
      }

      return c.json(
        createResponse(
          {
            conversationId: isUUID ? conversationId : targetConversations[0]?.id,
            conversationCode: conversationCode,
            autoReplyEnabled: enabled,
            changed: hasChanges,
            updatedCount: updateResult.count,
            affectedConversations: conversationIds,
          },
          `自动回复已${enabled ? '开启' : '关闭'}，影响${updateResult.count}个对话`,
        ),
      );
    } catch (error) {
      console.error(`[Auto Reply] Error updating conversation:`, error);
      return handleError(c, error);
    }
  },
);

// 批量设置多个对话的自动回复状态（可选功能）
conversationAutoReplyRoute.put(
  '/batch/auto-reply',
  zValidator(
    'json',
    z.object({
      conversationIds: z.array(z.string().uuid()),
      enabled: z.boolean(),
    }),
  ),
  async (c) => {
    try {
      const { conversationIds, enabled } = c.req.valid('json');

      if (conversationIds.length === 0) {
        return c.json(createErrorResponse('对话ID列表不能为空'), 400);
      }

      if (conversationIds.length > 100) {
        return c.json(createErrorResponse('一次最多只能操作100个对话'), 400);
      }

      // 先获取所有会话的当前状态，用于之后判断哪些需要发送通知
      const existingConversations = await prisma.conversation.findMany({
        where: {
          id: { in: conversationIds },
        },
        select: {
          id: true,
          organizationId: true,
          customerId: true,
          autoReplyEnabled: true,
        },
      });

      // 批量更新
      const result = await prisma.conversation.updateMany({
        where: {
          id: { in: conversationIds },
        },
        data: { autoReplyEnabled: enabled },
      });

      console.log(`[Auto Reply] Batch ${enabled ? 'enabled' : 'disabled'} auto reply for ${result.count} conversations`);

      // 如果有状态变化的会话，发送WebSocket通知
      if (result.count > 0 && existingConversations.length > 0) {
        // 找出那些状态会发生变化的会话
        const changedConversations = existingConversations.filter((conv) => conv.autoReplyEnabled !== enabled);

        console.log(`[Auto Reply] Found ${changedConversations.length} conversations with status change`);

        // 异步发送WebSocket通知
        Promise.all(
          changedConversations.map(async (conv) => {
            try {
              // 跳过没有organizationId的会话
              if (!conv.organizationId) {
                console.log(`[Auto Reply] Skipping conversation ${conv.id} without organizationId`);
                return;
              }

              // 获取客户信息
              const customer = await prisma.customer.findUnique({
                where: { id: conv.customerId },
                select: { nickname: true },
              });

              if (customer) {
                // 发送WebSocket通知
                notifyAutoReplyStatusChanged(conv.organizationId, conv.id, customer.nickname, enabled);
                console.log(`[Auto Reply] Notification sent for conversation ${conv.id}, customer: ${customer.nickname}, auto reply ${enabled ? 'enabled' : 'disabled'}`);
              }
            } catch (error) {
              console.error(`[Auto Reply] Error sending notification for conversation ${conv.id}:`, error);
            }
          }),
        ).catch((error) => {
          console.error(`[Auto Reply] Error processing batch notifications:`, error);
        });
      }

      return c.json(
        createResponse(
          {
            updatedCount: result.count,
            autoReplyEnabled: enabled,
          },
          `批量${enabled ? '开启' : '关闭'}自动回复成功，影响${result.count}个对话`,
        ),
      );
    } catch (error) {
      return handleError(c, error);
    }
  },
);

export default conversationAutoReplyRoute;
