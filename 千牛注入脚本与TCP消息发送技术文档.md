# 千牛注入脚本与TCP消息发送技术文档

## 概述

本文档详细说明易康无忧客服助手系统中千牛注入脚本的生成逻辑和TCP消息发送机制。系统通过注入JavaScript脚本到千牛客户端，建立WebSocket连接进行实时通信，并通过TCP服务器处理消息发送。

## 1. 千牛注入脚本生成流程

### 1.1 整体架构

```
连接邀请创建 → 脚本生成 → CDN上传 → 客户端注入 → WebSocket连接 → TCP通信
```

### 1.2 连接邀请创建

**API端点**: `POST /api/v1/connection-invitations-simple`

**核心逻辑**:
1. 创建连接邀请记录（ConnectionInvitation）
2. 生成唯一的邀请ID作为连接标识
3. 设置邀请过期时间（默认24小时）
4. 关联组织和团队信息

**数据结构**:
```typescript
interface ConnectionInvitation {
  id: string;              // 邀请ID，作为连接标识
  organizationId: string;  // 组织ID
  teamId: string;         // 团队ID
  name: string;           // 连接名称
  description?: string;   // 连接描述
  status: 'PENDING' | 'ACTIVATED' | 'EXPIRED' | 'REVOKED';
  expiresAt: DateTime;    // 过期时间
  cdnUrl?: string;        // CDN脚本URL
}
```

### 1.3 脚本生成逻辑

**API端点**: `GET /cdn-inject/qianniu-inject/:invitationId.js`

**生成流程**:

1. **邀请验证**
   ```typescript
   // 验证邀请是否存在且有效
   const invitation = await prisma.connectionInvitation.findUnique({
     where: { id: invitationId },
     include: { organization: true, team: true }
   });
   
   if (!invitation || invitation.status !== 'PENDING' || invitation.expiresAt < new Date()) {
     return error('邀请无效或已过期');
   }
   ```

2. **连接追踪记录**
   ```typescript
   // 记录脚本生成事件
   connectionTracker.trackScriptGeneration(
     invitationId,
     invitation.organizationId,
     invitation.teamId,
     { userAgent, clientIP }
   );
   ```

3. **WebSocket URL构建**
   ```typescript
   // 根据环境构建WebSocket连接URL
   let wsUrl: string;
   if (isDevelopment) {
     wsUrl = `ws://localhost:${port}/platform-ws/qianniu?invitationId=${invitationId}`;
   } else {
     const apiUrl = process.env.YKWY_ASSISTANT_API_URL;
     wsUrl = `${apiUrl.replace('https://', 'wss://')}/platform-ws/qianniu?invitationId=${invitationId}`;
   }
   ```

### 1.4 注入脚本结构

生成的JavaScript脚本包含以下核心组件：

1. **千牛原生脚本加载**
   ```javascript
   // 保持千牛原有功能
   const script = document.createElement("script");
   script.src = "https://iseiya.taobao.com/imsupport";
   document.getElementsByTagName("body")[0].appendChild(script);
   ```

2. **配置信息注入**
   ```javascript
   window.QIANNIU_CONFIG = {
     invitationId: '${invitation.id}',
     organizationId: '${invitation.organizationId}',
     teamId: '${invitation.teamId}',
     websocketUrl: '${wsUrl}',
     // ... 其他配置
   };
   ```

3. **WebSocket连接建立**
   ```javascript
   let socket = new WebSocket(window.QIANNIU_CONFIG.websocketUrl);
   
   socket.onopen = async (e) => {
     // 发送连接激活信息
     const activationMessage = {
       type: 'qianniu_connect',
       invitationId: window.QIANNIU_CONFIG.invitationId,
       clientInfo: {
         userAgent: navigator.userAgent,
         qianniuVersion: window._vs?.version,
         loginID: window._vs?.loginID,
         timestamp: Date.now()
       }
     };
     socket.send(JSON.stringify(activationMessage));
   };
   ```

4. **消息处理机制**
   ```javascript
   socket.onmessage = async function(event) {
     let param = JSON.parse(event.data);
     
     // 处理服务器命令执行
     if (param.method === 'execute') {
       try {
         const res = await eval(param.expression);
         socket.send(JSON.stringify({
           type: 'execute',
           response: JSON.stringify(res)
         }));
       } catch (err) {
         socket.send(JSON.stringify({
           type: 'execute',
           response: JSON.stringify({ error: err.message })
         }));
       }
     }
     
     // 处理千牛API调用
     else if (param.method === 'qianniu_api_call') {
       const apiResult = await window.callQianniuApi(param.apiParams);
       socket.send(JSON.stringify({
         type: 'qianniu_api_response',
         requestId: param.requestId,
         response: JSON.stringify(apiResult)
       }));
     }
   };
   ```

5. **心跳机制**
   ```javascript
   function startHeartbeat() {
     heartbeatInterval = setInterval(() => {
       if (socket.readyState === WebSocket.OPEN) {
         socket.send(JSON.stringify({
           type: 'ping',
           invitationId: window.QIANNIU_CONFIG.invitationId,
           timestamp: Date.now()
         }));
       }
     }, 30000); // 30秒心跳
   }
   ```

### 1.5 CDN上传机制

**服务类**: `CDNService`

**上传流程**:
1. 验证脚本内容有效性
2. 生成文件名：`${invitationId}.js`
3. 上传到MinIO/S3存储
4. 返回公开访问URL

```typescript
async uploadScript(invitationId: string, scriptContent: string): Promise<CDNUploadResult> {
  const key = `${invitationId}.js`;
  const fileData = Buffer.from(scriptContent, 'utf8');
  
  // 上传到S3
  await this.s3.write(key, fileData, {
    contentType: 'application/javascript',
    cacheControl: 'no-cache'
  });
  
  return {
    success: true,
    cdnUrl: `${this.cdnBaseUrl}/${this.bucketName}/${key}`,
    size: fileData.length
  };
}
```

## 2. WebSocket连接激活流程

### 2.1 连接建立

当千牛客户端加载注入脚本后，会自动建立WebSocket连接：

1. **连接升级处理**
   ```typescript
   // 处理千牛平台WebSocket连接
   if (url.pathname.startsWith('/platform-ws/')) {
     return handlePlatformWS(req, server, url);
   }
   ```

2. **邀请验证**
   ```typescript
   const invitationId = url.searchParams.get('invitationId');
   if (!invitationId) {
     return new Response('Missing invitationId parameter', { status: 400 });
   }
   ```

3. **连接数据初始化**
   ```typescript
   const success = server.upgrade(req, {
     data: {
       connectionId: invitationId, // 使用邀请ID作为连接ID
       type: 'invitation_pending',
       platformType: 'QIANNIU',
       invitationId,
       connectedAt: new Date(),
     },
   });
   ```

### 2.2 连接激活

当收到客户端的激活消息时：

1. **激活消息处理**
   ```typescript
   if (type === 'invitation_pending' && data.type === 'qianniu_connect') {
     await handleConnectionActivation(ws, data);
   }
   ```

2. **邀请验证与客户端创建**
   ```typescript
   // 验证邀请有效性
   const invitation = await prisma.connectionInvitation.findUnique({
     where: { id: invitationId },
     include: { organization: true, team: true }
   });
   
   // 创建千牛客户端记录
   const qianniuClient = await prisma.qianniuClient.create({
     data: {
       organizationId: invitation.organizationId,
       teamId: invitation.teamId,
       name: invitation.name,
       connectionId: ws.data.connectionId,
       isOnline: true,
       clientInfo: data.clientInfo
     }
   });
   ```

3. **连接状态升级**
   ```typescript
   // 升级连接为正式连接
   ws.data = {
     ...ws.data,
     type: 'activated',
     organizationId: invitation.organizationId,
     teamId: invitation.teamId,
     platformData: { clientId: qianniuClient.id }
   };
   
   // 添加到连接管理器
   platformWsManager.addConnection(connection);
   ```

## 3. TCP消息发送机制

### 3.1 TCP服务器架构

**服务类**: `QianniuTcpServer`
**监听端口**: 9997（可配置）

**核心组件**:
- TCP服务器：处理千牛客户端连接
- 连接映射：维护connectionId与TCP客户端的映射关系
- 消息队列：处理异步消息发送
- 响应处理：处理千牛客户端的响应

### 3.2 连接建立流程

1. **TCP客户端连接**
   ```typescript
   private handleClientConnected(socket: net.Socket) {
     const clientId = `${socket.remoteAddress}:${socket.remotePort}`;
     this.clients.set(clientId, socket);
     
     // 立即发送获取用户名请求
     const payload = JSON.stringify({ method: 'getusername' });
     socket.write(payload + '\r\n\r\n');
   }
   ```

2. **用户名响应处理**
   ```typescript
   private async handleUsernameResponse(clientId: string, messageData: any) {
     const username = messageData.username || messageData.nick || messageData.loginID;
     
     // 通过用户名查找对应的connectionId
     const client = await prisma.qianniuClient.findFirst({
       where: { 
         clientInfo: { path: ['loginID'], equals: username },
         isOnline: true 
       }
     });
     
     if (client) {
       // 建立映射关系
       this.connectionIdToClientId.set(client.connectionId, clientId);
       this.clientIdToConnectionId.set(clientId, client.connectionId);
     }
   }
   ```

### 3.3 消息发送逻辑

**API端点**: `POST /api/v1/qianniu-tcp/send-message`

**发送流程**:

1. **连接查找**
   ```typescript
   async sendTextMessage(connectionId: string, targetId: string, text: string): Promise<boolean> {
     // 查找对应的Socket连接
     const socket = this.getSocketByConnectionId(connectionId);
     if (!socket) {
       return false;
     }
   }
   ```

2. **消息分段处理**
   ```typescript
   // 检查是否为多段消息（按换行符分割）
   const messageSegments = this.splitMessageByNewlines(text);
   
   if (messageSegments.length > 1) {
     // 分段发送消息
     return await this.sendMessageSegments(socket, targetId, messageSegments, keep);
   } else {
     // 单段消息，直接发送
     return await this.sendSingleMessage(socket, targetId, text, keep);
   }
   ```

3. **分段发送机制**
   ```typescript
   private async sendMessageSegments(socket, targetId, segments, keep): Promise<boolean> {
     for (let i = 0; i < segments.length; i++) {
       const segment = segments[i];
       
       // 发送当前段落
       await this.sendSingleMessage(socket, targetId, segment, keep);
       
       // 如果不是最后一段，添加随机延迟
       if (i < segments.length - 1) {
         const delay = Math.floor(Math.random() * 2000) + 2000; // 2-4秒随机延迟
         await new Promise(resolve => setTimeout(resolve, delay));
       }
     }
   }
   ```

4. **单条消息发送**
   ```typescript
   private async sendSingleMessage(socket, targetId, text, keep): Promise<boolean> {
     const payload = JSON.stringify({
       method: 'sendtext',
       id: targetId,
       text: text,
       keep: keep,
     });
     
     const fullMessage = payload + '\r\n\r\n';
     socket.write(fullMessage);
     return true;
   }
   ```

### 3.4 消息格式规范

**千牛TCP消息格式**:
```typescript
interface TcpMessage {
  method: string;    // 操作方法：sendtext, sendimage, sendvideo等
  id?: string;       // 目标用户ID
  text?: string;     // 文本内容
  image?: string;    // 图片路径
  video?: string;    // 视频路径
  keep?: string;     // 保持连接：on/off
  flash?: string;    // 高亮提醒：open/close
  username?: string; // 用户名（响应中）
  success?: boolean; // 操作结果
  message?: string;  // 响应消息
}
```

**消息终止符**: `\r\n\r\n`

### 3.5 支持的操作类型

1. **文本消息发送**
   ```json
   {
     "method": "sendtext",
     "id": "buyer_nick",
     "text": "消息内容",
     "keep": "on"
   }
   ```

2. **图片消息发送**
   ```json
   {
     "method": "sendimage",
     "id": "buyer_nick",
     "image": "/path/to/image.jpg",
     "keep": "off"
   }
   ```

3. **视频消息发送**
   ```json
   {
     "method": "sendvideo",
     "id": "buyer_nick",
     "video": "/path/to/video.mp4",
     "keep": "off"
   }
   ```

4. **高亮提醒**
   ```json
   {
     "method": "highlight",
     "id": "buyer_nick",
     "flash": "open"
   }
   ```

5. **关闭会话**
   ```json
   {
     "method": "closetalker",
     "id": "buyer_nick"
   }
   ```

## 4. 错误处理与监控

### 4.1 连接状态监控

1. **连接追踪器**
   ```typescript
   class ConnectionTracker {
     trackScriptGeneration(invitationId, organizationId, teamId, metadata);
     trackWebSocketActivation(invitationId, metadata);
     trackTcpConnection(invitationId, clientId, metadata);
     trackFullyLinked(invitationId, metadata);
   }
   ```

2. **心跳机制**
   - WebSocket心跳：30秒间隔
   - TCP连接检测：通过数据传输检测
   - 连接状态实时更新

### 4.2 异常处理

1. **脚本生成失败**
   - 邀请验证失败
   - CDN上传失败
   - 返回错误脚本

2. **连接建立失败**
   - WebSocket连接超时
   - 激活消息格式错误
   - 数据库操作失败

3. **消息发送失败**
   - TCP连接断开
   - 消息格式错误
   - 目标用户不存在

### 4.3 日志记录

系统使用结构化日志记录所有关键操作：

```typescript
logger.info('千牛消息发送', {
  connectionId,
  targetId,
  messageLength: text.length,
  segmentCount: segments.length,
  timestamp: new Date().toISOString()
});
```

## 5. 部署说明

### 5.1 环境变量配置

```bash
# API服务配置
YKWY_ASSISTANT_API_URL=https://api.example.com
PORT=3002

# TCP服务配置
TCP_PORT=9997

# CDN配置
CDN_BASE_URL=https://cdn.example.com
CDN_BUCKET_NAME=qianniu-scripts
CDN_ACCESS_KEY=your_access_key
CDN_SECRET_KEY=your_secret_key

# 数据库配置
DATABASE_URL=********************************/db
```

### 5.2 部署架构

```
[千牛客户端] ←→ [TCP:9997] ←→ [API服务器:3000] ←→ [WebSocket] ←→ [Web前端]
                    ↓
               [PostgreSQL数据库]
                    ↓
                [CDN存储服务]
```

### 5.3 安全考虑

1. **脚本注入安全**
   - 邀请ID验证
   - 过期时间控制
   - 访问日志记录

2. **TCP通信安全**
   - 连接映射验证
   - 消息内容过滤
   - 频率限制

3. **WebSocket安全**
   - 连接认证
   - 消息格式验证
   - 异常连接检测

## 总结

千牛注入脚本与TCP消息发送机制是整个客服助手系统的核心组件，通过精心设计的连接管理、消息路由和错误处理机制，实现了稳定可靠的千牛平台集成。系统支持多租户、多团队的复杂业务场景，具有良好的扩展性和可维护性。
