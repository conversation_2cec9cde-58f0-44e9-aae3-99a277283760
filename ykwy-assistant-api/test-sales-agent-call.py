#!/usr/bin/env python3
"""
模拟销售智能体调用千牛API的测试脚本
"""

import requests
import json
import os

# 配置
API_BASE = "http://localhost:3002"
FIXED_TOKEN = "f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09"

def test_qianniu_api():
    """测试千牛API调用"""
    print("🤖 模拟销售智能体调用千牛API...")
    print("=" * 50)
    
    # 测试用的请求头
    headers = {
        'Access-Token': FIXED_TOKEN,
        'Content-Type': 'application/json',
        'User-Agent': 'SalesAgent/1.0'
    }
    
    # 测试1: 获取千牛连接列表
    print("📋 测试1: 获取千牛连接列表")
    try:
        response = requests.get(f"{API_BASE}/api/v1/qianniu-api/connections", headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()
    
    # 测试2: 模拟发送商品卡片
    print("📋 测试2: 模拟发送商品卡片")
    try:
        payload = {
            "connectionId": "test-connection-id",
            "customerId": "test-customer-id",
            "batchItemIds": ["123456", "789012"]
        }
        response = requests.post(
            f"{API_BASE}/api/v1/qianniu-api/send-item-card", 
            headers=headers,
            json=payload
        )
        print(f"状态码: {response.status_code}")
        if response.status_code in [200, 400]:  # 400可能是因为测试数据无效
            data = response.json()
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()
    
    # 测试3: 模拟邀请下单
    print("📋 测试3: 模拟邀请下单")
    try:
        payload = {
            "connectionId": "test-connection-id",
            "customerId": "test-customer-id",
            "itemProps": '[{"itemId":"123456","skuId":"789012","quantity":1}]',
            "buyerNick": "test-buyer",
            "bizDomain": "taobao",
            "encrypType": "internal"
        }
        response = requests.post(
            f"{API_BASE}/api/v1/qianniu-api/invite-order", 
            headers=headers,
            json=payload
        )
        print(f"状态码: {response.status_code}")
        if response.status_code in [200, 400]:  # 400可能是因为测试数据无效
            data = response.json()
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()
    
    print("=" * 50)
    print("✅ 销售智能体API调用测试完成！")

if __name__ == "__main__":
    test_qianniu_api()
