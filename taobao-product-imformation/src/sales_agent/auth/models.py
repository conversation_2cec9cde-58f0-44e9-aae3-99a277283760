"""
认证相关的数据模型
"""
from enum import Enum
from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"


class User(BaseModel):
    """用户模型"""
    id: str
    username: str
    email: str  # 使用str类型避免email-validator依赖
    role: UserRole
    is_active: bool = True
    created_at: datetime
    updated_at: Optional[datetime] = None


class UserCreate(BaseModel):
    """创建用户请求模型"""
    username: str
    email: str  # 使用str类型避免email-validator依赖
    password: str
    role: UserRole = UserRole.USER


class UserLogin(BaseModel):
    """用户登录请求模型"""
    username: str
    password: str


class Token(BaseModel):
    """JWT令牌响应模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User


class TokenData(BaseModel):
    """JWT令牌数据模型"""
    user_id: str
    username: str
    role: UserRole
    exp: int
