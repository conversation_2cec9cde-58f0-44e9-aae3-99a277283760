# 数据库配置
# docker 启动 ykwy-api，此时localhost指向docker容器的地址，所以需要通过docker网络访问
DATABASE_URL="********************************************/ykwy-assistant"

# JWT 认证配置
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long-for-security"

# 固定访问令牌配置（供其他服务调用时验证）
FIXED_ACCESS_TOKENS="f8e7d6c5b4a39281706f5*****281706f5e4d3c2b1a09"

# 调用销售智能体时使用的令牌
YKWY_SALES_BOT_ACCESS_TOKEN="销售智能体的令牌值"

# CDN配置 - MinIO
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET_NAME=
CDN_BASE_URL=

YKWY_ASSISTANT_WEB_URL=
YKWY_ASSISTANT_API_URL="https://ykwy-assistant-api.wuyoutansuo.com"
PORT=3000 #开发环境不建议
HOST=0.0.0.0 #开发环境不建议
TCP_PORT=9997
NODE_ENV=development

# Loki日志服务配置
LOKI_URL=
LOKI_USERNAME=
LOKI_PASSWORD=

# 销售智能体配置
SALES_AGENT_URL="http://localhost:8000"
SALES_AGENT_AUTO_REPLY="true"
