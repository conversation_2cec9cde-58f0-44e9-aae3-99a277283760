"""
认证路由
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTTPAuthorizationCredentials

from .models import UserLogin, UserCreate, Token, User, UserRole
from .jwt_auth import create_access_token
from .user_service import authenticate_user, create_user, get_all_users, initialize_default_users
from .middleware import get_current_user, security

logger = logging.getLogger(__name__)

# 创建认证路由器
router = APIRouter(prefix="/auth", tags=["authentication"])


@router.post("/login", response_model=Token)
async def login(user_data: UserLogin):
    """用户登录"""
    try:
        user = await authenticate_user(user_data.username, user_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        token_data = create_access_token(user)
        logger.info(f"用户登录成功: {user.username}")
        
        return Token(**token_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/register", response_model=User)
async def register(user_data: UserCreate):
    """用户注册"""
    try:
        user = await create_user(user_data)
        logger.info(f"用户注册成功: {user.username}")
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.get("/me", response_model=User)
async def get_current_user_info(current_user = Depends(get_current_user)):
    """获取当前用户信息"""
    from .user_service import get_user_by_id
    user = await get_user_by_id(current_user.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user


@router.get("/users", response_model=list[User])
async def list_users(current_user = Depends(get_current_user)):
    """获取用户列表（仅管理员）"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    
    users = await get_all_users()
    return users


@router.post("/verify")
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证令牌"""
    try:
        current_user = await get_current_user(credentials)
        return {
            "valid": True,
            "user_id": current_user.user_id,
            "username": current_user.username,
            "role": current_user.role
        }
    except HTTPException:
        return {"valid": False}


@router.post("/initialize")
async def initialize_users():
    """初始化默认用户（开发用）"""
    try:
        await initialize_default_users()
        return {
            "success": True,
            "message": "Default users initialized",
            "users": [
                {"username": "admin", "password": "admin123", "role": "admin"},
                {"username": "user", "password": "user123", "role": "user"}
            ]
        }
    except Exception as e:
        logger.error(f"初始化用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize users"
        )
