#!/usr/bin/env node
/**
 * 固定访问令牌自动化测试脚本
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3002';
const FIXED_TOKEN_1 = 'f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09';
const FIXED_TOKEN_2 = 'a1b2c3d4e5f6789012345678abcdef90123456789abcdef0123456789abcdef01';
const INVALID_TOKEN = 'invalid-token-123';

class AccessTokenTester {
  constructor() {
    this.results = [];
    this.passed = 0;
    this.failed = 0;
  }

  async test(name, method, url, headers = {}, expectedStatus = 200) {
    console.log(`\n🧪 ${name}`);
    console.log(`   ${method} ${url}`);

    try {
      const response = await fetch(url, {
        method,
        headers,
        timeout: 10000,
      });

      const statusOk = response.status === expectedStatus;
      
      if (statusOk) {
        console.log(`   ✅ 状态码: ${response.status} (预期: ${expectedStatus})`);
        this.passed++;
      } else {
        console.log(`   ❌ 状态码: ${response.status} (预期: ${expectedStatus})`);
        this.failed++;
      }

      // 显示响应内容
      try {
        const responseText = await response.text();
        let responseData;
        try {
          responseData = JSON.parse(responseText);
        } catch {
          responseData = responseText;
        }

        const responseStr = typeof responseData === 'object' 
          ? JSON.stringify(responseData, null, 2) 
          : responseData;
        
        const truncated = responseStr.length > 200 
          ? responseStr.substring(0, 200) + '...' 
          : responseStr;
        
        console.log(`   📄 响应: ${truncated}`);
      } catch (error) {
        console.log(`   📄 响应读取失败: ${error.message}`);
      }

      this.results.push({
        name,
        method,
        url,
        status: response.status,
        expectedStatus,
        passed: statusOk,
      });

    } catch (error) {
      console.log(`   ❌ 请求失败: ${error.message}`);
      this.failed++;
      this.results.push({
        name,
        method,
        url,
        error: error.message,
        passed: false,
      });
    }

    // 避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  async runAllTests() {
    console.log('🚀 开始固定访问令牌测试');
    console.log('=' * 60);

    // 测试1: 健康检查
    await this.test(
      '健康检查',
      'GET',
      `${BASE_URL}/api/v1/access-token-test/health`
    );

    // 测试2: 固定令牌验证 - Access-Token 头
    await this.test(
      '固定令牌验证 (Access-Token头)',
      'POST',
      `${BASE_URL}/api/v1/access-token-test/verify`,
      { 'Access-Token': FIXED_TOKEN_1 }
    );

    // 测试3: 固定令牌验证 - Authorization Bearer
    await this.test(
      '固定令牌验证 (Authorization Bearer)',
      'POST',
      `${BASE_URL}/api/v1/access-token-test/verify`,
      { 'Authorization': `Bearer ${FIXED_TOKEN_1}` }
    );

    // 测试4: 无效令牌
    await this.test(
      '无效令牌验证 (应该失败)',
      'POST',
      `${BASE_URL}/api/v1/access-token-test/verify`,
      { 'Access-Token': INVALID_TOKEN },
      401
    );

    // 测试5: 缺少认证头
    await this.test(
      '缺少认证头 (应该失败)',
      'POST',
      `${BASE_URL}/api/v1/access-token-test/verify`,
      {},
      401
    );

    // 测试6: 混合认证
    await this.test(
      '混合认证测试',
      'POST',
      `${BASE_URL}/api/v1/access-token-test/hybrid-verify`,
      { 'Access-Token': FIXED_TOKEN_1 }
    );

    // 测试7: 获取用户信息
    await this.test(
      '获取当前用户信息',
      'GET',
      `${BASE_URL}/api/v1/access-token-test/me`,
      { 'Authorization': `Bearer ${FIXED_TOKEN_2}` }
    );

    // 测试8: 管理员权限测试
    await this.test(
      '管理员权限测试',
      'GET',
      `${BASE_URL}/api/v1/access-token-test/admin-test`,
      { 'Access-Token': FIXED_TOKEN_1 }
    );

    // 测试9: 实际API接口 - 品牌管理
    await this.test(
      '品牌管理接口 (固定令牌)',
      'GET',
      `${BASE_URL}/api/v1/brands`,
      { 'Access-Token': FIXED_TOKEN_1 }
    );

    // 测试10: 实际API接口 - 无效令牌
    await this.test(
      '品牌管理接口 (无效令牌，应该失败)',
      'GET',
      `${BASE_URL}/api/v1/brands`,
      { 'Access-Token': INVALID_TOKEN },
      401
    );
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试总结');
    console.log('='.repeat(60));

    const total = this.passed + this.failed;
    const successRate = total > 0 ? (this.passed / total * 100) : 0;

    console.log(`总测试数: ${total}`);
    console.log(`通过: ${this.passed} ✅`);
    console.log(`失败: ${this.failed} ❌`);
    console.log(`成功率: ${successRate.toFixed(1)}%`);

    if (this.failed > 0) {
      console.log(`\n❌ 失败的测试:`);
      for (const result of this.results) {
        if (!result.passed) {
          console.log(`   - ${result.name}`);
        }
      }
    }

    console.log(`\n🔑 测试令牌:`);
    console.log(`   销售智能体: ${FIXED_TOKEN_1}`);
    console.log(`   管理后台: ${FIXED_TOKEN_2}`);

    if (successRate >= 80) {
      console.log(`\n🎉 测试基本通过！固定访问令牌功能正常工作。`);
    } else {
      console.log(`\n⚠️  测试失败较多，请检查API服务和令牌配置。`);
    }

    console.log(`\n💡 配置提醒:`);
    console.log(`   确保在 .env 文件中设置了 FIXED_ACCESS_TOKENS`);
    console.log(`   格式: 销售智能体:token1|管理后台:token2`);
  }
}

async function main() {
  const tester = new AccessTokenTester();
  await tester.runAllTests();
  tester.printSummary();
}

// 运行测试
main().catch(console.error);
