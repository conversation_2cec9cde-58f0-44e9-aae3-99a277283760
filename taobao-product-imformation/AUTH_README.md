# 认证系统使用指南

## 概述

为 `taobao-production-imformation` 和 `ykwy-assistant-api` 项目添加了完整的路由守卫和认证系统。

## ykwy-assistant-api 项目

### 已添加的路由守卫

所有 `/api/v1/*` 路由现在都需要 JWT 认证，除了：
- `/api/auth/*` - 认证相关路由（登录、注册等）
- `/api/v1/cdn-inject/*` - CDN注入（公开访问）
- `/health` - 健康检查

### 权限级别

- **普通用户**: 可以访问基本功能
- **组织管理员**: 可以管理用户和组织
- **系统管理员**: 拥有所有权限

### 使用方法

1. 登录获取 JWT 令牌：
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

2. 使用令牌访问受保护的路由：
```bash
curl -X GET http://localhost:3000/api/v1/users \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## taobao-production-imformation 项目

### 新增的认证系统

完整的 JWT 认证系统，包括：
- 用户管理
- 角色权限控制
- JWT 令牌验证
- 路由保护

### 默认用户

系统启动时会自动创建默认用户：
- **管理员**: `admin` / `admin123`
- **普通用户**: `user` / `user123`

### API 端点

#### 认证相关
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `GET /auth/me` - 获取当前用户信息
- `GET /auth/users` - 获取用户列表（仅管理员）
- `POST /auth/verify` - 验证令牌
- `POST /auth/initialize` - 初始化默认用户

#### 受保护的路由

以下路由需要认证：
- `POST /vector/sync` - 同步向量数据（管理员）
- `POST /vector/initialize` - 初始化向量存储（管理员）
- `DELETE /vector/collections/{name}` - 删除集合（管理员）
- `POST /vector/import-qa-excel` - 导入Excel（认证用户）
- `POST /rebuild-index` - 重建索引（管理员）
- `POST /vector/sync` - 同步向量数据（管理员）
- `POST /sync` - 同步数据（认证用户）
- `GET /debug/dialogues` - 调试数据（管理员）

#### 公开路由

以下路由不需要认证：
- `GET /` - 首页
- `GET /health` - 健康检查
- `GET /dashboard` - 仪表板
- `GET /brands` - 品牌列表
- `POST /chat` - 聊天接口
- `GET /conversation/{id}/history` - 对话历史

### 使用示例

1. **初始化默认用户**：
```bash
curl -X POST http://localhost:8000/auth/initialize
```

2. **用户登录**：
```bash
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

3. **访问受保护的路由**：
```bash
curl -X POST http://localhost:8000/vector/sync \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

4. **获取当前用户信息**：
```bash
curl -X GET http://localhost:8000/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 环境变量配置

可以通过环境变量配置认证系统：

```bash
# JWT 密钥（生产环境必须更改）
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production

# JWT 令牌过期时间（分钟）
JWT_EXPIRE_MINUTES=1440
```

## 测试认证系统

运行测试脚本验证认证系统：

```bash
cd taobao-product-imformation
python test_auth.py
```

测试脚本会验证：
- 健康检查
- 用户初始化
- 登录功能
- 路由保护
- 权限控制

## 安全注意事项

1. **生产环境**：
   - 必须更改默认的 JWT 密钥
   - 使用强密码
   - 启用 HTTPS

2. **令牌管理**：
   - JWT 令牌默认有效期 24 小时
   - 令牌包含用户信息和权限
   - 客户端应安全存储令牌

3. **权限控制**：
   - 管理员操作需要 ADMIN 角色
   - 敏感操作都有权限检查
   - 用户只能访问授权的资源

## 故障排除

1. **401 Unauthorized**：
   - 检查是否提供了有效的 JWT 令牌
   - 确认令牌格式：`Bearer YOUR_TOKEN`

2. **403 Forbidden**：
   - 检查用户权限级别
   - 确认操作是否需要管理员权限

3. **500 Internal Server Error**：
   - 检查服务器日志
   - 确认数据库连接正常
