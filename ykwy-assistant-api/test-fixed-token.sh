#!/bin/bash

# 固定访问令牌测试脚本

API_BASE="http://localhost:3002"
# 注意：这里需要替换为实际的令牌值
FIXED_TOKEN="f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09"

echo "🧪 开始测试固定访问令牌功能..."
echo "=================================="

# 测试1: 使用 Access-Token 头访问千牛API
echo "📋 测试1: 使用 Access-Token 头访问千牛API"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -H "Access-Token: $FIXED_TOKEN" \
  -H "Content-Type: application/json" \
  "$API_BASE/api/v1/qianniu-api/connections" | head -10
echo ""

# 测试2: 使用 Authorization Bearer 访问千牛API
echo "📋 测试2: 使用 Authorization Bearer 访问千牛API"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -H "Authorization: Bearer $FIXED_TOKEN" \
  -H "Content-Type: application/json" \
  "$API_BASE/api/v1/qianniu-api/connections" | head -10
echo ""

# 测试3: 使用无效令牌（应该返回401）
echo "📋 测试3: 使用无效令牌（应该返回401）"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -H "Access-Token: invalid-token" \
  -H "Content-Type: application/json" \
  "$API_BASE/api/v1/qianniu-api/connections"
echo ""

# 测试4: 不提供令牌（应该返回401）
echo "📋 测试4: 不提供令牌（应该返回401）"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -H "Content-Type: application/json" \
  "$API_BASE/api/v1/qianniu-api/connections"
echo ""

# 测试5: 测试其他路由（应该需要JWT认证）
echo "📋 测试5: 测试其他路由（应该需要JWT认证）"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -H "Access-Token: $FIXED_TOKEN" \
  -H "Content-Type: application/json" \
  "$API_BASE/api/v1/users/me"
echo ""

# 测试6: 健康检查（无需认证）
echo "📋 测试6: 健康检查（无需认证）"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  "$API_BASE/health"
echo ""

echo "=================================="
echo "✅ 测试完成！"
