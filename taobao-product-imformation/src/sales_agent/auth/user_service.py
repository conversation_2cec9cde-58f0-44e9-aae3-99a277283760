"""
用户服务模块
"""
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from .models import User, UserCreate, UserRole
from .jwt_auth import hash_password, verify_password


# 简单的内存用户存储（生产环境应该使用数据库）
_users_db: Dict[str, Dict[str, Any]] = {}


async def create_user(user_data: UserCreate) -> User:
    """创建用户"""
    # 检查用户名是否已存在
    for user in _users_db.values():
        if user["username"] == user_data.username or user["email"] == user_data.email:
            raise ValueError("Username or email already exists")
    
    user_id = str(uuid.uuid4())
    hashed_password = hash_password(user_data.password)
    
    user_dict = {
        "id": user_id,
        "username": user_data.username,
        "email": user_data.email,
        "password": hashed_password,
        "role": user_data.role.value,
        "is_active": True,
        "created_at": datetime.utcnow(),
        "updated_at": None
    }
    
    _users_db[user_id] = user_dict
    
    return User(
        id=user_id,
        username=user_data.username,
        email=user_data.email,
        role=user_data.role,
        is_active=True,
        created_at=user_dict["created_at"]
    )


async def get_user_by_id(user_id: str) -> Optional[User]:
    """根据ID获取用户"""
    user_dict = _users_db.get(user_id)
    if not user_dict:
        return None
    
    return User(
        id=user_dict["id"],
        username=user_dict["username"],
        email=user_dict["email"],
        role=UserRole(user_dict["role"]),
        is_active=user_dict["is_active"],
        created_at=user_dict["created_at"],
        updated_at=user_dict.get("updated_at")
    )


async def get_user_by_username(username: str) -> Optional[User]:
    """根据用户名获取用户"""
    for user_dict in _users_db.values():
        if user_dict["username"] == username:
            return User(
                id=user_dict["id"],
                username=user_dict["username"],
                email=user_dict["email"],
                role=UserRole(user_dict["role"]),
                is_active=user_dict["is_active"],
                created_at=user_dict["created_at"],
                updated_at=user_dict.get("updated_at")
            )
    return None


async def authenticate_user(username: str, password: str) -> Optional[User]:
    """用户认证"""
    for user_dict in _users_db.values():
        if user_dict["username"] == username:
            if verify_password(password, user_dict["password"]):
                return User(
                    id=user_dict["id"],
                    username=user_dict["username"],
                    email=user_dict["email"],
                    role=UserRole(user_dict["role"]),
                    is_active=user_dict["is_active"],
                    created_at=user_dict["created_at"],
                    updated_at=user_dict.get("updated_at")
                )
    return None


async def get_all_users() -> List[User]:
    """获取所有用户"""
    users = []
    for user_dict in _users_db.values():
        users.append(User(
            id=user_dict["id"],
            username=user_dict["username"],
            email=user_dict["email"],
            role=UserRole(user_dict["role"]),
            is_active=user_dict["is_active"],
            created_at=user_dict["created_at"],
            updated_at=user_dict.get("updated_at")
        ))
    return users


async def initialize_default_users():
    """初始化默认用户"""
    # 创建默认管理员用户
    admin_exists = any(user["role"] == UserRole.ADMIN.value for user in _users_db.values())
    
    if not admin_exists:
        admin_user = UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            role=UserRole.ADMIN
        )
        await create_user(admin_user)
        print("✅ 默认管理员用户已创建: admin/admin123")
    
    # 创建默认普通用户
    user_exists = any(user["username"] == "user" for user in _users_db.values())
    
    if not user_exists:
        normal_user = UserCreate(
            username="user",
            email="<EMAIL>", 
            password="user123",
            role=UserRole.USER
        )
        await create_user(normal_user)
        print("✅ 默认普通用户已创建: user/user123")
