import { Hono } from 'hono';

import { logger } from '../lib/logger';
import { hybridAuth } from '../middleware/access-token';
// 导入认证中间件
import { requireRole } from '../middleware/auth';
// 导入所有路由模块
import accessTokenTestRoute from './access-token-test';
import authRoute from './auth';
import brandsRoute from './brands';
import cdnInjectRoute from './cdn-inject';
import connectionDiagnosticsRoute from './connection-diagnostics';
import connectionInvitationsRoute from './connection-invitations-simple';
import conversationAutoReplyRoute from './conversationAutoReply';
import conversationsRoute from './conversations';
import messagesRoute from './messages';
import organizationInvitationsRoute from './organization-invitations';
import platformWsRoute from './platform-ws';
import qianniuAccountsRoute from './qianniu-accounts';
import qianniuApiRoute from './qianniu-api';
import qianniuClientsRoute from './qianniu-clients';
import qianniuTcpRoute from './qianniu-tcp';
import salesAgentRoute from './salesAgent';
import teamsRoute from './teams';
import usersRoute from './users';

const routes = new Hono();

// 健康检查
routes.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API v1 路由
const apiV1 = new Hono();

logger.info('开始注册API v1路由');

// 为需要认证的路由添加混合认证中间件（支持JWT和固定访问令牌）
apiV1.use('/users/*', hybridAuth);
apiV1.use('/teams/*', hybridAuth);
apiV1.use('/conversations/*', hybridAuth);
apiV1.use('/messages/*', hybridAuth);
apiV1.use('/conversation-auto-reply/*', hybridAuth);
apiV1.use('/connection-invitations/*', hybridAuth);
apiV1.use('/qianniu-clients/*', hybridAuth);
apiV1.use('/qianniu-accounts/*', hybridAuth);
apiV1.use('/brands/*', hybridAuth);
apiV1.use('/qianniu-tcp/*', hybridAuth);
apiV1.use('/qianniu-api/*', hybridAuth);
apiV1.use('/platform-ws/*', hybridAuth);
apiV1.use('/salesAgent/*', hybridAuth);
apiV1.use('/connection/*', hybridAuth);

// 为需要管理员权限的路由添加额外权限检查
apiV1.use('/organization-invitations/*', hybridAuth, requireRole('SYSTEM_ADMIN', 'ORGANIZATION_ADMIN'));
apiV1.use('/users/*', requireRole('ORGANIZATION_ADMIN', 'SYSTEM_ADMIN')); // 用户管理需要组织管理员权限

// ---- 用户和组织管理 ---- //
apiV1.route('/auth', authRoute); // 用户认证（不需要认证）
apiV1.route('/users', usersRoute); // 用户管理（需要认证）
apiV1.route('/teams', teamsRoute); // 团队管理（需要认证）

// ---- 会话和消息管理 ---- //
apiV1.route('/conversations', messagesRoute); // 把消息挂在会话之下（需要认证）
apiV1.route('/messages', messagesRoute); // 消息管理（需要认证）
apiV1.route('/conversations', conversationsRoute); // 对话管理（需要认证）
apiV1.route('/conversation-auto-reply', conversationAutoReplyRoute); // 对话自动回复控制（需要认证）

// ---- 组织和邀请 ---- //
apiV1.route('/organization-invitations', organizationInvitationsRoute); // 组织邀请（需要认证）

// ---- 连接和客户端管理 ---- //
apiV1.route('/connection-invitations', connectionInvitationsRoute); // 连接邀请管理（需要认证）
apiV1.route('/qianniu-clients', qianniuClientsRoute); // 千牛客户端管理（需要认证）
apiV1.route('/qianniu-accounts', qianniuAccountsRoute); // 千牛账号管理（需要认证）

// ---- 品牌和后台管理 ---- //
apiV1.route('/brands', brandsRoute); // 品牌管理（需要认证）

// ---- 千牛TCP和API ---- //
apiV1.route('/qianniu-tcp', qianniuTcpRoute); // 千牛TCP服务（需要认证）
apiV1.route('/qianniu-api', qianniuApiRoute); // 千牛API代理（需要认证）

// ---- CDN注入 ---- //
apiV1.route('/cdn-inject', cdnInjectRoute); // CDN注入（不需要认证，公开访问）

// ---- 平台WebSocket监控 ---- //
apiV1.route('/platform-ws', platformWsRoute); // 平台WebSocket连接监控（需要认证）

// ---- 销售智能体集成 ---- //
apiV1.route('/salesAgent', salesAgentRoute); // 销售智能体自动回复配置（需要认证）

// ---- 连接诊断 ---- //
apiV1.route('/connection', connectionDiagnosticsRoute); // 连接诊断（需要认证）

// ---- 访问令牌测试 ---- //
apiV1.route('/access-token-test', accessTokenTestRoute); // 访问令牌测试（包含认证测试）

// 根路径信息
apiV1.get('/', (c) => {
  return c.json({
    message: 'YKWY Customer Service API v1',
    description: '客服系统 API，提供完整的客服管理功能',
    endpoints: {
      conversations: '/api/v1/conversations',
      messages: '/api/v1/messages',
      teams: '/api/v1/teams',
      organizationInvitations: '/api/v1/organization-invitations',
      connectionInvitations: '/api/v1/connection-invitations',
      qianniuClients: '/api/v1/qianniu-clients',
      qianniuAccounts: '/api/v1/qianniu-accounts',
      brands: '/api/v1/brands',
      qianniuTcp: '/api/v1/qianniu-tcp',
      qianniuApi: '/api/v1/qianniu-api',
      platformWs: '/api/v1/platform-ws',
      salesAgent: '/api/v1/salesAgent',

      connection: '/api/v1/connection',
      // 认证
      auth: '/api/auth/*',
    },
    features: ['组织和成员管理', '多平台客户管理', '实时对话系统', '消息发送和撤回', 'AI 智能推荐', '销售智能体自动回复', '任务分配和跟踪', '数据统计分析', '用户权限管理', '活动记录追踪'],
    docs: '详细 API 文档请参考各个端点的具体使用方法',
  });
});

routes.route('/api/v1', apiV1);

// 认证路由（不需要认证）
routes.route('/api/auth', authRoute);

// CDN路由（不需要认证）
routes.route('/cdn', cdnInjectRoute);

export default routes;
