{"name": "ykwy-assistant-api", "version": "0.0.5", "description": "易康无忧-客服助手API接口", "private": true, "type": "module", "scripts": {"dev": "bun --watch src/index.ts", "build": "bun build src/index.ts --outdir=dist --target=bun", "start": "bun dist/index.js", "lint": "eslint src --ext ts --report-unused-disable-directives", "lint:fix": "eslint \"{src,test}/**/*.ts\" --fix --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "format": "prettier --write \"{src,test}/**/*.{ts,js,json,md}\"", "format:check": "prettier --check \"{src,test}/**/*.{ts,js,json,md}\"", "fix:all": "bun run format && bun run lint:fix", "typecheck": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:seed": "bun prisma/seed.ts", "db:studio": "prisma studio", "postinstall": "husky", "tsc": "tsc --noEmit"}, "dependencies": {"@hono/zod-validator": "^0.7.0", "@prisma/client": "^6.11.1", "hono": "^4.8.4", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.30.1", "@stylistic/eslint-plugin": "^5.1.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "bun-types": "latest", "eslint": "^9.30.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "husky": "^9.1.7", "prisma": "^6.11.1", "typescript-eslint": "^8.35.1", "typescript": "^5.8.3"}}