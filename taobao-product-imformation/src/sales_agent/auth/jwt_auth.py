"""
JWT认证模块
"""
import os
import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from .models import User, UserRole, TokenData


class JWTAuth:
    """JWT认证类"""
    
    def __init__(self):
        self.secret_key = os.environ.get("JWT_SECRET_KEY", "your-super-secret-jwt-key-change-in-production")
        self.algorithm = "HS256"
        self.access_token_expire_minutes = int(os.environ.get("JWT_EXPIRE_MINUTES", "1440"))  # 24小时
    
    def create_access_token(self, user: User) -> Dict[str, Any]:
        """创建访问令牌"""
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "user_id": user.id,
            "username": user.username,
            "role": user.role.value,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60,  # 转换为秒
            "user": user
        }
    
    def verify_token(self, token: str) -> Optional[TokenData]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌类型
            if payload.get("type") != "access":
                return None
            
            # 检查是否过期
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
                return None
            
            return TokenData(
                user_id=payload.get("user_id"),
                username=payload.get("username"),
                role=UserRole(payload.get("role")),
                exp=exp
            )
        except jwt.InvalidTokenError:
            return None
        except Exception:
            return None
    
    def extract_token_from_header(self, authorization: Optional[str]) -> Optional[str]:
        """从Authorization头中提取令牌"""
        if not authorization:
            return None
        
        parts = authorization.split(" ")
        if len(parts) != 2 or parts[0].lower() != "bearer":
            return None
        
        return parts[1]


# 全局JWT认证实例
jwt_auth = JWTAuth()


def create_access_token(user: User) -> Dict[str, Any]:
    """创建访问令牌的便捷函数"""
    return jwt_auth.create_access_token(user)


def verify_token(token: str) -> Optional[TokenData]:
    """验证令牌的便捷函数"""
    return jwt_auth.verify_token(token)


def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()


def verify_password(password: str, hashed_password: str) -> bool:
    """验证密码"""
    return hash_password(password) == hashed_password
