#!/usr/bin/env python3
"""
修复email-validator问题的脚本
"""
import os
import subprocess
import sys

def check_email_validator():
    """检查email-validator是否已安装"""
    try:
        import email_validator
        print("✅ email-validator 已安装")
        return True
    except ImportError:
        print("❌ email-validator 未安装")
        return False

def install_email_validator():
    """安装email-validator"""
    print("🔧 正在安装 email-validator...")
    
    try:
        # 尝试使用poetry安装
        result = subprocess.run(["poetry", "add", "email-validator"], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 使用poetry安装成功")
            return True
    except:
        pass
    
    try:
        # 尝试使用pip安装
        result = subprocess.run([sys.executable, "-m", "pip", "install", "email-validator"], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 使用pip安装成功")
            return True
    except:
        pass
    
    print("❌ 安装失败")
    return False

def use_string_fallback():
    """使用字符串作为email类型的备选方案"""
    print("🔄 使用字符串类型作为备选方案...")
    
    models_file = "src/sales_agent/auth/models.py"
    if not os.path.exists(models_file):
        print(f"❌ 找不到文件: {models_file}")
        return False
    
    # 读取文件
    with open(models_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换EmailStr为str
    content = content.replace("from pydantic import BaseModel, EmailStr", 
                             "from pydantic import BaseModel")
    content = content.replace("email: EmailStr", 
                             "email: str  # 使用str类型避免email-validator依赖")
    
    # 写回文件
    with open(models_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修改为使用字符串类型")
    return True

def test_import():
    """测试导入是否成功"""
    try:
        from src.sales_agent.auth.models import User, UserRole, TokenData
        print("✅ 模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    print("🔧 email-validator 问题修复工具")
    print("=" * 50)
    
    # 检查当前状态
    if check_email_validator():
        if test_import():
            print("🎉 一切正常，无需修复")
            return
    
    # 尝试安装email-validator
    if install_email_validator():
        if check_email_validator() and test_import():
            print("🎉 问题已解决")
            return
    
    # 使用备选方案
    print("\n⚠️  无法安装email-validator，使用备选方案...")
    if use_string_fallback():
        if test_import():
            print("🎉 使用备选方案成功")
            print("💡 现在可以启动API服务了: python main.py")
        else:
            print("❌ 备选方案也失败了")
    
    print("\n📝 手动解决方法:")
    print("1. 运行: poetry install")
    print("2. 或者: pip install email-validator")
    print("3. 或者: 手动修改models.py中的EmailStr为str")

if __name__ == "__main__":
    main()
