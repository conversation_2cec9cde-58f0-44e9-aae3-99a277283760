### 🔐 固定访问令牌认证测试
###
### 测试目的: 验证固定访问令牌功能是否正常工作
### 令牌类型: 64字符安全16进制令牌
### 权限级别: 管理员权限
###

@baseUrl = http://localhost:8000
@shopId = 54898e9a-e7d2-4351-9f74-6e45d74ec153
@validToken = f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09
@invalidToken = invalid-token-123

### ✅ 测试1: 健康检查 (无需认证)
GET {{baseUrl}}/health

### ✅ 测试2: 有效令牌验证
POST {{baseUrl}}/auth/verify
Authorization: Bearer {{validToken}}

### ❌ 测试3: 无效令牌验证 (应该失败)
POST {{baseUrl}}/auth/verify
Authorization: Bearer {{invalidToken}}

### ✅ 测试4: 获取当前用户信息
GET {{baseUrl}}/auth/me
Authorization: Bearer {{validToken}}

### ✅ 测试5: 数据同步 (需要认证)
POST {{baseUrl}}/sync
Content-Type: application/json
Authorization: Bearer {{validToken}}

{
  "shop_id": "{{shopId}}",
  "incremental": true,
  "sync_products": false,
  "sync_qa": false
}

### ❌ 测试6: 数据同步无认证头 (应该失败)
POST {{baseUrl}}/sync
Content-Type: application/json

{
  "shop_id": "{{shopId}}",
  "incremental": true,
  "sync_products": false,
  "sync_qa": false
}

### ❌ 测试7: 数据同步错误令牌 (应该失败)
POST {{baseUrl}}/sync
Content-Type: application/json
Authorization: Bearer {{invalidToken}}

{
  "shop_id": "{{shopId}}",
  "incremental": true,
  "sync_products": false,
  "sync_qa": false
}

### ✅ 测试8: 聊天功能 (无需认证)
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "你好，测试消息",
  "brand_id": "qinggong",
  "shop_id": "{{shopId}}"
}

### ✅ 测试9: 向量数据同步 (需要管理员权限)
POST {{baseUrl}}/vector/sync
Authorization: Bearer {{validToken}}

### ✅ 测试10: 获取用户列表 (需要管理员权限)
GET {{baseUrl}}/auth/users
Authorization: Bearer {{validToken}}

### ✅ 测试11: 重建索引 (需要管理员权限)
POST {{baseUrl}}/rebuild-index?brand_id=qinggong
Authorization: Bearer {{validToken}}

### ✅ 测试12: 获取调试对话数据 (需要管理员权限)
GET {{baseUrl}}/debug/dialogues
Authorization: Bearer {{validToken}}

### ✅ 测试13: 只同步产品数据 (需要认证)
POST {{baseUrl}}/sync/products?shop_id={{shopId}}&incremental=true
Authorization: Bearer {{validToken}}

### ❌ 测试14: 只同步产品数据无认证 (应该失败)
POST {{baseUrl}}/sync/products?shop_id={{shopId}}&incremental=true

### ✅ 测试15: 只同步问答数据 (需要认证)
POST {{baseUrl}}/sync/qa?shop_id={{shopId}}&incremental=true
Authorization: Bearer {{validToken}}

### ❌ 测试16: 只同步问答数据无认证 (应该失败)
POST {{baseUrl}}/sync/qa?shop_id={{shopId}}&incremental=true

### 📊 预期结果:
### 测试1: 200 OK - 健康检查成功
### 测试2: 200 OK - 令牌验证成功，返回用户信息
### 测试3: 200 OK - 返回 {"valid": false}
### 测试4: 200 OK - 返回固定管理员用户信息
### 测试5: 200 OK - 同步成功
### 测试6: 403 Forbidden - 缺少认证
### 测试7: 401 Unauthorized - 无效令牌
### 测试8: 200 OK - 聊天响应
### 测试9: 200 OK - 向量同步成功
### 测试10: 200 OK - 返回用户列表
### 测试11: 200 OK - 重建索引成功
### 测试12: 200 OK - 返回调试数据
### 测试13: 200 OK - 产品同步成功
### 测试14: 403 Forbidden - 缺少认证
### 测试15: 200 OK - 问答同步成功
### 测试16: 403 Forbidden - 缺少认证
