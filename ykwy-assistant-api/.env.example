# 数据库配置
# docker 启动 ykwy-api，此时localhost指向docker容器的地址，所以需要通过docker网络访问
DATABASE_URL="********************************************/ykwy-assistant"

# JWT 认证配置
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long-for-security"

# 固定访问令牌配置（仅用于千牛API）
# 支持两种格式：
# 1. 简单格式：token1|token2|token3
# 2. 带标识格式：销售智能体:token1|千牛客户端:token2
FIXED_ACCESS_TOKENS="销售智能体:f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09|千牛客户端:a1b2c3d4e5f6789012345678abcdef90123456789abcdef0123456789abcdef01"

# CDN配置 - MinIO
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET_NAME=
CDN_BASE_URL=

YKWY_ASSISTANT_WEB_URL=
YKWY_ASSISTANT_API_URL="https://ykwy-assistant-api.wuyoutansuo.com"
PORT=3000 #开发环境不建议
HOST=0.0.0.0 #开发环境不建议
TCP_PORT=9997
NODE_ENV=development

# Loki日志服务配置
LOKI_URL=
LOKI_USERNAME=
LOKI_PASSWORD=

# 销售智能体配置
SALES_AGENT_URL="http://localhost:8000"
SALES_AGENT_AUTO_REPLY="true"
