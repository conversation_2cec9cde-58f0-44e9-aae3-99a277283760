# 固定访问令牌使用指南 - ykwy-assistant-api

## 🎯 概述

为 `ykwy-assistant-api` 添加了固定访问令牌支持，实现与 `taobao-product-imformation` 和 `ykwy-api` 项目类似的认证机制。

### 特性
- ✅ **双重认证支持**：JWT + 固定访问令牌
- ✅ **多种请求头格式**：`Access-Token` 和 `Authorization: Bearer`
- ✅ **带标识的令牌**：支持 `服务名:令牌` 格式
- ✅ **自动管理员权限**：固定令牌自动获得 `SYSTEM_ADMIN` 权限
- ✅ **向后兼容**：不影响现有JWT认证

## 🔧 配置

### 1. 环境变量配置

在 `.env` 文件中添加：

```bash
# 固定访问令牌配置
# 支持两种格式：
# 1. 简单格式：token1|token2|token3
# 2. 带标识格式：服务名:token1|服务名:token2
FIXED_ACCESS_TOKENS="销售智能体:f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09|管理后台:a1b2c3d4e5f6789012345678abcdef90123456789abcdef0123456789abcdef01"
```

### 2. 令牌生成

建议使用安全的随机令牌：

```bash
# 生成64字符16进制令牌
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# 或使用Python
python3 -c "import secrets; print(secrets.token_hex(32))"
```

## 🚀 使用方法

### 1. HTTP请求头格式

#### 方式1：Access-Token 头
```bash
curl -X GET "http://localhost:3002/api/v1/brands" \
  -H "Access-Token: f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09"
```

#### 方式2：Authorization Bearer
```bash
curl -X GET "http://localhost:3002/api/v1/brands" \
  -H "Authorization: Bearer f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09"
```

#### 方式3：X-Access-Token 头
```bash
curl -X GET "http://localhost:3002/api/v1/brands" \
  -H "X-Access-Token: f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09"
```

### 2. JavaScript/前端示例

```javascript
// 使用 Access-Token 头
const response = await fetch('/api/v1/brands', {
  headers: {
    'Access-Token': 'f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09',
    'Content-Type': 'application/json'
  }
});

// 使用 Authorization Bearer
const response2 = await fetch('/api/v1/qianniu-clients', {
  headers: {
    'Authorization': 'Bearer f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09',
    'Content-Type': 'application/json'
  }
});
```

## 🛡️ 认证机制

### 混合认证流程

1. **优先检查固定令牌**：
   - 检查 `Access-Token` 头
   - 检查 `X-Access-Token` 头
   - 检查 `Authorization: Bearer` 头（非JWT格式）

2. **回退到JWT认证**：
   - 如果没有固定令牌，使用JWT验证
   - 保持向后兼容

### 权限级别

- **固定令牌用户**：
  - 用户ID：`fixed-token-{标识}`
  - 角色：`SYSTEM_ADMIN`
  - 权限：最高管理员权限
  - 组织：不绑定特定组织

- **JWT用户**：
  - 保持原有权限体系
  - 基于数据库用户信息

## 📊 路由守卫覆盖

### 已更新为混合认证的路由

所有原本使用 `jwtAuth` 的路由现在都支持固定访问令牌：

- `/api/v1/users/*` - 用户管理
- `/api/v1/teams/*` - 团队管理
- `/api/v1/conversations/*` - 对话管理
- `/api/v1/messages/*` - 消息管理
- `/api/v1/connection-invitations/*` - 连接邀请
- `/api/v1/qianniu-clients/*` - 千牛客户端
- `/api/v1/qianniu-accounts/*` - 千牛账号
- `/api/v1/brands/*` - 品牌管理
- `/api/v1/qianniu-tcp/*` - 千牛TCP
- `/api/v1/qianniu-api/*` - 千牛API
- `/api/v1/platform-ws/*` - 平台WebSocket
- `/api/v1/salesAgent/*` - 销售智能体
- `/api/v1/organization-invitations/*` - 组织邀请
- `/api/v1/connection/*` - 连接诊断

### 公开路由（无需认证）

- `/health` - 健康检查
- `/api/auth/*` - 认证相关
- `/cdn/*` - CDN注入

## 🧪 测试

### 1. REST客户端测试

使用 `test/access-token.rest` 文件：

```bash
# 在VS Code中安装REST Client插件
# 打开 test/access-token.rest 文件
# 点击每个测试上方的"Send Request"按钮
```

### 2. 自动化测试

```bash
# 确保API服务运行在 localhost:3002
cd ykwy-assistant-api
node test/run-access-token-tests.js
```

### 3. 专用测试接口

访问 `/api/v1/access-token-test/*` 进行功能测试：

- `GET /api/v1/access-token-test/health` - 健康检查
- `POST /api/v1/access-token-test/verify` - 固定令牌验证
- `POST /api/v1/access-token-test/hybrid-verify` - 混合认证验证
- `GET /api/v1/access-token-test/me` - 获取当前用户信息
- `GET /api/v1/access-token-test/admin-test` - 管理员权限测试

## 🔄 与其他项目集成

### 与 taobao-product-imformation 集成

```javascript
// taobao-product-imformation 调用 ykwy-assistant-api
const response = await fetch('http://localhost:3002/api/v1/salesAgent', {
  headers: {
    'Access-Token': 'f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09',
    'Content-Type': 'application/json'
  }
});
```

### 与 ykwy-api 集成

```javascript
// ykwy-api 调用 ykwy-assistant-api
const response = await fetch('http://localhost:3002/api/v1/brands', {
  headers: {
    'Access-Token': 'a1b2c3d4e5f6789012345678abcdef90123456789abcdef0123456789abcdef01',
    'Content-Type': 'application/json'
  }
});
```

## 🔒 安全最佳实践

### 1. 令牌管理

- ✅ 使用强随机令牌（至少32字节）
- ✅ 定期轮换令牌
- ✅ 为不同服务使用不同令牌
- ✅ 使用带标识的令牌格式便于管理

### 2. 环境隔离

```bash
# 开发环境
FIXED_ACCESS_TOKENS="开发测试:dev-token-here"

# 生产环境
FIXED_ACCESS_TOKENS="销售智能体:prod-token-1|管理后台:prod-token-2"
```

### 3. 监控和日志

- ✅ 记录认证成功/失败日志
- ✅ 监控令牌使用情况
- ✅ 设置异常访问告警

## 📝 故障排除

### 常见问题

1. **401 Unauthorized**
   - 检查令牌是否正确
   - 确认环境变量配置
   - 验证请求头格式

2. **500 Internal Server Error**
   - 检查 `FIXED_ACCESS_TOKENS` 环境变量是否设置
   - 查看服务器日志

3. **令牌不生效**
   - 确认服务已重启
   - 检查令牌格式（不要包含空格）
   - 验证环境变量加载

### 调试步骤

1. 检查环境变量：
   ```bash
   echo $FIXED_ACCESS_TOKENS
   ```

2. 测试健康检查：
   ```bash
   curl http://localhost:3002/api/v1/access-token-test/health
   ```

3. 查看服务器日志确认令牌验证过程

## 🎉 总结

现在 `ykwy-assistant-api` 具有完整的固定访问令牌支持：

- ✅ **完全兼容**：不影响现有JWT认证
- ✅ **灵活配置**：支持多种令牌格式
- ✅ **安全可靠**：自动管理员权限，详细日志
- ✅ **易于测试**：提供完整的测试工具
- ✅ **服务集成**：便于与其他项目集成

您现在可以使用固定访问令牌轻松访问所有需要认证的API接口！
