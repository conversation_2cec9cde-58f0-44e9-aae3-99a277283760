# ==================== 敏感信息和环境相关配置 ====================

# OpenAI API配置（必需）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1

# 千牛API服务地址（必需，不同环境地址不同）
QIANNIU_API_BASE_URL=http://localhost:3002

# YKWY API配置（必需，用于向量存储数据同步）
YKWY_API_BASE_URL=https://ykwy-api.wuyoutansuo.com
YKWY_API_TOKEN=PoNyP4f0zYDWuXg8NytR

# YKWY Assistant API配置（必需，用于千牛API调用）
YKWY_ASSISTANT_API_TOKEN=f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09

# 向量数据库配置
CHROMA_DB_PATH=./data/chroma_db

# Loki日志服务配置（与ykwy-assistant-api统一）
LOKI_URL="https://log-api.kenny.pro/loki/api/v1/push"
LOKI_USERNAME="loki_writer"
LOKI_PASSWORD="AtCgmPC2TBUz7yqyJyy5"
LOKI_SYNC_MODE=true

# 环境配置
NODE_ENV=development

# ==================== 使用说明 ====================
# 1. 复制此文件为 .env
# 2. 填入真实的API密钥和服务地址
# 3. 其他配置项（超时时间、重试次数等）已在代码中设置默认值
# 4. 如需修改其他参数，请直接修改 qianniu_config.py 文件
