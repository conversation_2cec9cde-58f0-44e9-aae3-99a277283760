import type { Context, Next } from 'hono';

import { logger } from '../lib/logger';

// 扩展 Hono 的 Context 类型，添加固定令牌用户信息
declare module 'hono' {
  interface ContextVariableMap {
    fixedTokenUser?: {
      id: string;
      type: 'fixed_token';
      role: 'SYSTEM_ADMIN';
      source: string;
    };
  }
}

/**
 * 固定访问令牌认证中间件（仅用于销售智能体调用千牛API）
 */
export async function fixedTokenAuth(c: Context, next: Next): Promise<Response | void> {
  try {
    // 支持两种请求头格式
    const accessToken = c.req.header('Access-Token') || c.req.header('X-Access-Token');
    const authHeader = c.req.header('Authorization');

    let token: string | null = null;
    let tokenSource = 'unknown';

    // 优先检查 Access-Token 头
    if (accessToken && accessToken.trim() !== '') {
      token = accessToken.trim();
      tokenSource = 'Access-Token';
    }
    // 然后检查 Authorization Bearer 格式
    else if (authHeader && authHeader.startsWith('Bearer ')) {
      const bearerToken = authHeader.substring(7).trim();
      if (bearerToken) {
        token = bearerToken;
        tokenSource = 'Authorization-Bearer';
      }
    }

    if (!token) {
      logger.debug('固定令牌认证失败：未提供令牌', {
        path: c.req.path,
        method: c.req.method,
        headers: {
          'Access-Token': !!c.req.header('Access-Token'),
          'X-Access-Token': !!c.req.header('X-Access-Token'),
          'Authorization': !!c.req.header('Authorization'),
        },
      });
      return c.json({ error: 'Access token required' }, 401);
    }

    // 获取配置的访问密钥
    const configuredToken = process.env['FIXED_ACCESS_TOKENS'];

    if (!configuredToken || configuredToken.trim() === '') {
      logger.error('固定令牌认证失败：未配置访问密钥', {
        path: c.req.path,
        method: c.req.method,
      });
      return c.json({ error: 'Access token authentication not configured' }, 500);
    }

    // 验证令牌
    if (token !== configuredToken.trim()) {
      logger.warn('固定令牌认证失败：无效令牌', {
        path: c.req.path,
        method: c.req.method,
        tokenSource,
        tokenPreview: token.substring(0, 8) + '...',
      });
      return c.json({ error: 'Invalid access token' }, 401);
    }

    // 设置固定令牌用户信息到上下文
    c.set('fixedTokenUser', {
      id: 'fixed-token-sales-agent',
      type: 'fixed_token',
      role: 'SYSTEM_ADMIN',
      source: 'sales-agent',
    });

    // 同时设置兼容的用户信息（兼容现有的JWT认证逻辑）
    c.set('user', {
      id: 'fixed-token-sales-agent',
      email: '<EMAIL>',
      role: 'SYSTEM_ADMIN',
      organizationId: undefined, // 固定令牌不绑定特定组织
      teamId: undefined,
    });

    logger.info('固定令牌认证成功', {
      path: c.req.path,
      method: c.req.method,
      tokenSource,
      userId: 'fixed-token-sales-agent',
    });

    await next();
  } catch (error) {
    logger.error('固定令牌认证中间件错误', {
      path: c.req.path,
      method: c.req.method,
    }, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Access token authentication failed' }, 500);
  }
}

/**
 * 混合认证中间件：支持JWT和固定访问令牌
 * 优先尝试固定令牌，然后回退到JWT
 */
export async function hybridAuth(c: Context, next: Next): Promise<Response | void> {
  try {
    // 首先尝试固定令牌认证
    const accessToken = c.req.header('Access-Token') || c.req.header('X-Access-Token');
    const authHeader = c.req.header('Authorization');

    // 如果有 Access-Token 头，优先使用固定令牌认证
    if (accessToken && accessToken.trim() !== '') {
      return await fixedTokenAuth(c, next);
    }

    // 如果 Authorization 头是固定令牌格式（不是JWT），使用固定令牌认证
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const bearerToken = authHeader.substring(7).trim();
      // 简单判断：JWT通常包含两个点，固定令牌通常不包含
      if (bearerToken && !bearerToken.includes('.')) {
        return await fixedTokenAuth(c, next);
      }
    }

    // 回退到JWT认证
    const { jwtAuth } = await import('./auth');
    return await jwtAuth(c, next);
  } catch (error) {
    logger.error('混合认证中间件错误', {
      path: c.req.path,
      method: c.req.method,
    }, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Authentication failed' }, 401);
  }
}

/**
 * 可选的混合认证中间件（不强制要求认证）
 */
export async function optionalHybridAuth(c: Context, next: Next) {
  try {
    const accessToken = c.req.header('Access-Token') || c.req.header('X-Access-Token');
    const authHeader = c.req.header('Authorization');

    // 如果没有任何认证头，直接继续
    if (!accessToken && !authHeader) {
      await next();
      return;
    }

    // 尝试认证，但失败时不返回错误
    try {
      await hybridAuth(c, next);
    } catch {
      logger.debug('可选混合认证失败，继续执行', {
        path: c.req.path,
        method: c.req.method,
      });
      await next();
    }
  } catch (error) {
    logger.error('可选混合认证中间件错误', {
      path: c.req.path,
      method: c.req.method,
    }, error instanceof Error ? error : new Error(String(error)));
    // 可选认证失败时不返回错误，继续执行
    await next();
  }
}

/**
 * 检查是否为固定令牌用户
 */
export function isFixedTokenUser(c: Context): boolean {
  const fixedTokenUser = c.get('fixedTokenUser');
  return !!fixedTokenUser;
}

/**
 * 获取当前认证用户信息（兼容JWT和固定令牌）
 */
export function getCurrentAuthUser(c: Context) {
  const fixedTokenUser = c.get('fixedTokenUser');
  if (fixedTokenUser) {
    return {
      id: fixedTokenUser.id,
      type: 'fixed_token' as const,
      role: fixedTokenUser.role,
      source: fixedTokenUser.source,
    };
  }

  const jwtUser = c.get('user');
  if (jwtUser) {
    return {
      id: jwtUser.id,
      type: 'jwt' as const,
      role: jwtUser.role,
      email: jwtUser.email,
      organizationId: jwtUser.organizationId,
      teamId: jwtUser.teamId,
    };
  }

  return null;
}
