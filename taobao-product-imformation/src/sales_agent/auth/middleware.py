"""
认证中间件
"""
import os
from functools import wraps
from typing import Optional, List
from fastapi import HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials

from .jwt_auth import jwt_auth
from .models import TokenData, UserRole
from .user_service import get_user_by_id


# HTTP Bearer 认证方案
security = HTTPBearer()

# 固定访问令牌配置（安全生成的64字符16进制代码）
FIXED_ACCESS_TOKEN = os.environ.get("FIXED_ACCESS_TOKENS", "f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09")


def verify_fixed_token(token: str) -> Optional[TokenData]:
    """验证固定访问令牌（16进制代码）"""
    if token == FIXED_ACCESS_TOKEN:
        # 为固定令牌创建一个默认的管理员用户数据
        return TokenData(
            user_id="fixed-admin-user",
            username="admin",
            role=UserRole.ADMIN,
            exp=9999999999  # 永不过期
        )
    return None


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """获取当前用户 - 支持JWT令牌和固定访问令牌"""
    token = credentials.credentials

    # 首先尝试验证固定访问令牌
    fixed_token_data = verify_fixed_token(token)
    if fixed_token_data:
        return fixed_token_data

    # 如果不是固定令牌，则尝试JWT验证
    token_data = jwt_auth.verify_token(token)

    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 验证用户是否存在且活跃
    user = await get_user_by_id(token_data.user_id)
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return token_data


async def get_optional_user(authorization: Optional[str] = None) -> Optional[TokenData]:
    """可选的用户认证（不强制要求认证）"""
    if not authorization:
        return None

    token = jwt_auth.extract_token_from_header(authorization)
    if not token:
        return None

    token_data = jwt_auth.verify_token(token)
    if not token_data:
        return None

    # 验证用户是否存在且活跃
    user = await get_user_by_id(token_data.user_id)
    if not user or not user.is_active:
        return None

    return token_data


def require_roles(allowed_roles: List[UserRole]):
    """角色权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )

            if current_user.role not in allowed_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )

            return await func(*args, **kwargs)
        return wrapper
    return decorator


def auth_required(func):
    """认证必需装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        current_user = kwargs.get('current_user')
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        return await func(*args, **kwargs)
    return wrapper


def admin_required(func):
    """管理员权限必需装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        current_user = kwargs.get('current_user')
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )

        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin privileges required"
            )

        return await func(*args, **kwargs)
    return wrapper
