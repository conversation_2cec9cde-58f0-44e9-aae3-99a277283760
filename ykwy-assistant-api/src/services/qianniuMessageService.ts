import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { normalizeConversationCode } from '../lib/utils';
import { qianniuTcpServer } from './qianniuTcpServer';

/**
 * 千牛消息发送服务
 * 负责将前端消息转发到对应的千牛客户端
 */
export class QianniuMessageService {
  /**
   * 发送消息到千牛客户端
   * @param conversationId 对话ID
   * @param message 消息内容
   */
  async sendMessageToQianniu(conversationId: string, message: Record<string, unknown>): Promise<boolean> {
    try {
      logger.info('尝试向千牛发送消息', { conversationId });

      // 1. 根据对话ID查找对应的千牛账号和客户端信息
      const conversationInfo = await this.getConversationInfo(conversationId);
      if (!conversationInfo) {
        logger.warn('对话未找到', { conversationId });
        return false;
      }

      // 2. 检查千牛客户端连接ID
      const connectionId = conversationInfo.qianniuAccount.client.connectionId;
      logger.debug('千牛客户端连接ID', { conversationId, connectionId });

      if (!connectionId) {
        logger.warn('对话缺少连接ID', { conversationId });
        return false;
      }

      // 3. 检查客户昵称和消息内容
      const customerNickname = conversationInfo.customer.nickname;
      const messageContent = message['content'] as string;

      logger.debug('消息发送详情', {
        conversationId,
        customerNickname,
        messageLength: messageContent?.length || 0,
      });

      if (!customerNickname) {
        logger.error('客户昵称缺失', { conversationId });
        return false;
      }

      if (!messageContent) {
        logger.error('消息内容缺失', { conversationId });
        return false;
      }

      // 4. 直接发送消息到千牛TCP服务器（参考qianniu-send-msg.txt）
      // 使用客户的platformCustomerId作为targetId，这是千牛的客户ID格式
      const targetId = conversationInfo.customer.platformCustomerId || customerNickname;
      const success = await qianniuTcpServer.sendTextMessage(connectionId, targetId, messageContent);

      logger.debug('千牛消息发送结果', { conversationId, success });

      if (success) {
        logger.info('千牛消息发送成功', { conversationId });
      } else {
        logger.error('千牛消息发送失败', { conversationId });
      }

      return success;
    } catch (error) {
      logger.error('千牛消息发送异常', { conversationId }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 广播消息到相同conversationCode的所有对话
   * @param conversationId 发起对话ID
   * @param message 消息内容
   */
  async broadcastMessageToQianniu(conversationId: string, message: Record<string, unknown>): Promise<boolean> {
    try {
      logger.info('开始广播消息到相同conversationCode的对话', { conversationId });

      // 1. 获取当前对话的conversationCode和相关信息
      const currentConversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        select: {
          conversationCode: true,
          organizationId: true,
          qianniuAccountId: true,
        },
      });

      if (!currentConversation?.conversationCode) {
        logger.warn('当前对话缺少conversationCode，回退到单一发送', { conversationId });
        return await this.sendMessageToQianniu(conversationId, message);
      }

      // 2. 标准化conversationCode
      const normalizedCode = normalizeConversationCode(currentConversation.conversationCode);
      if (!normalizedCode) {
        logger.warn('conversationCode标准化失败，回退到单一发送', {
          conversationId,
          originalCode: currentConversation.conversationCode,
        });
        return await this.sendMessageToQianniu(conversationId, message);
      }

      // 3. 查找所有相同conversationCode的对话
      const relatedConversations = await prisma.conversation.findMany({
        where: {
          OR: [
            {
              conversationCode: normalizedCode, // 精确匹配
              status: { not: 'CLOSED' },
            },
            {
              conversationCode: { startsWith: `${normalizedCode}#` }, // 前缀匹配
              status: { not: 'CLOSED' },
            },
          ],
          organizationId: currentConversation.organizationId, // 确保在同一组织内
        },
        include: {
          qianniuAccount: {
            include: {
              client: {
                select: {
                  connectionId: true,
                },
              },
            },
          },
          customer: {
            select: {
              nickname: true,
              platformCustomerId: true,
            },
          },
        },
      });

      logger.info('找到相同conversationCode的对话，准备广播', {
        conversationId,
        normalizedCode,
        totalConversations: relatedConversations.length,
        conversationIds: relatedConversations.map((c) => c.id),
      });

      // 4. 广播消息到所有相关对话
      const sendResults = await Promise.allSettled(
        relatedConversations.map(async (conversation) => {
          const connectionId = conversation.qianniuAccount.client.connectionId;
          const customerNickname = conversation.customer.nickname;
          const messageContent = message['content'] as string;

          if (!connectionId) {
            logger.warn('对话缺少连接ID，跳过', { conversationId: conversation.id });
            return false;
          }

          if (!customerNickname || !messageContent) {
            logger.warn('对话缺少必要信息，跳过', {
              conversationId: conversation.id,
              hasNickname: !!customerNickname,
              hasContent: !!messageContent,
            });
            return false;
          }

          // 使用客户的platformCustomerId作为targetId
          const targetId = conversation.customer.platformCustomerId || customerNickname;
          const success = await qianniuTcpServer.sendTextMessage(connectionId, targetId, messageContent);

          logger.debug('广播消息发送结果', {
            conversationId: conversation.id,
            connectionId,
            targetId,
            success,
          });

          return success;
        }),
      );

      // 5. 统计发送结果
      const successCount = sendResults.filter((result) => result.status === 'fulfilled' && result.value === true).length;
      const totalCount = relatedConversations.length;

      logger.info('广播消息发送完成', {
        conversationId,
        normalizedCode,
        totalConversations: totalCount,
        successfulSends: successCount,
        failedSends: totalCount - successCount,
      });

      // 如果至少有一个发送成功，认为广播成功
      return successCount > 0;
    } catch (error) {
      logger.error('广播消息发送异常', { conversationId }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 获取对话相关信息
   */
  private async getConversationInfo(conversationId: string) {
    return await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        qianniuAccount: {
          include: {
            client: {
              select: {
                id: true,
                connectionId: true,
                isOnline: true,
                teamId: true,
                organizationId: true,
              },
            },
          },
        },
        customer: {
          select: {
            id: true,
            nickname: true,
            platformCustomerId: true,
          },
        },
      },
    });
  }

  /**
   * 批量发送消息到多个千牛客户端
   * @param messages 消息列表，包含对话ID和消息内容
   */
  async sendBatchMessages(messages: Array<{ conversationId: string; message: Record<string, unknown> }>): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const { conversationId, message } of messages) {
      const result = await this.sendMessageToQianniu(conversationId, message);
      if (result) {
        success++;
      } else {
        failed++;
      }
    }

    logger.info('批量消息发送完成', { successCount: success, failedCount: failed });
    return { success, failed };
  }

  /**
   * 检查千牛客户端连接状态
   */
  async checkQianniuClientStatus(conversationId: string): Promise<{ isOnline: boolean; clientInfo?: Record<string, unknown> }> {
    try {
      const conversationInfo = await this.getConversationInfo(conversationId);
      if (!conversationInfo) {
        return { isOnline: false };
      }

      const client = conversationInfo.qianniuAccount.client;
      return {
        isOnline: client.isOnline && !!client.connectionId,
        clientInfo: {
          clientId: client.id,
          connectionId: client.connectionId,
          teamId: client.teamId,
          organizationId: client.organizationId,
        },
      };
    } catch (error) {
      logger.error('检查千牛客户端状态失败', {}, error instanceof Error ? error : new Error(String(error)));
      return { isOnline: false };
    }
  }
}

// 创建并导出单例
export const qianniuMessageService = new QianniuMessageService();
