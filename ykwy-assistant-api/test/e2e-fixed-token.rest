### 固定访问令牌 E2E 测试

@baseUrl = http://localhost:3002
@fixedToken = f8e7d6c5b4a39281706f5e4d3c2***c5b4a39281706f5e4d3c2b1a09

### 1. 测试千牛API - 使用 Access-Token 头（应该成功）
GET {{baseUrl}}/api/v1/qianniu-api/connections
Access-Token: {{fixedToken}}
Content-Type: application/json

### 2. 测试千牛API - 使用 Authorization Bearer（应该成功）
GET {{baseUrl}}/api/v1/qianniu-api/connections
Authorization: Bearer {{fixedToken}}
Content-Type: application/json

### 3. 测试千牛API - 无效令牌（应该返回 401）
GET {{baseUrl}}/api/v1/qianniu-api/connections
Access-Token: invalid-token-12345
Content-Type: application/json

### 4. 测试千牛API - 无令牌（应该返回 401）
GET {{baseUrl}}/api/v1/qianniu-api/connections
Content-Type: application/json

### 5. 测试千牛API - 发送商品卡片（模拟销售智能体调用）
POST {{baseUrl}}/api/v1/qianniu-api/send-item-card
Access-Token: {{fixedToken}}
Content-Type: application/json

{
  "connectionId": "test-connection-id",
  "customerId": "test-customer-id",
  "batchItemIds": ["123456", "789012"]
}

### 6. 测试千牛API - 邀请下单（模拟销售智能体调用）
POST {{baseUrl}}/api/v1/qianniu-api/invite-order
Access-Token: {{fixedToken}}
Content-Type: application/json

{
  "connectionId": "test-connection-id",
  "customerId": "test-customer-id",
  "itemProps": "[{\"itemId\":\"123456\",\"skuId\":\"789012\",\"quantity\":1}]",
  "buyerNick": "test-buyer",
  "bizDomain": "taobao",
  "encrypType": "internal"
}

### 7. 测试非千牛API路由 - 使用固定令牌（应该返回 401，需要JWT）
GET {{baseUrl}}/api/v1/users/me
Access-Token: {{fixedToken}}
Content-Type: application/json

### 8. 测试健康检查 - 无需认证（应该成功）
GET {{baseUrl}}/health

### 9. 测试销售智能体推荐API - 使用固定令牌（应该返回 401，需要JWT）
GET {{baseUrl}}/api/v1/salesAgent/recommendations/conversation/test-conversation-id
Access-Token: {{fixedToken}}
Content-Type: application/json
