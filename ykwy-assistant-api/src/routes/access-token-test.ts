import { Hono } from 'hono';

import { logger } from '../lib/logger';
import { fixedTokenAuth, hybridAuth, getCurrentAuthUser, isFixedTokenUser } from '../middleware/access-token';

const router = new Hono();

/**
 * 测试固定访问令牌认证
 */
router.post('/verify', fixedTokenAuth, async (c) => {
  const authUser = getCurrentAuthUser(c);
  const isFixed = isFixedTokenUser(c);

  logger.info('固定访问令牌验证成功', {
    authUser,
    isFixed,
    path: c.req.path,
  });

  return c.json({
    valid: true,
    user: authUser,
    type: 'fixed_token',
    message: '固定访问令牌验证成功',
  });
});

/**
 * 测试混合认证（JWT + 固定令牌）
 */
router.post('/hybrid-verify', hybridAuth, async (c) => {
  const authUser = getCurrentAuthUser(c);
  const isFixed = isFixedTokenUser(c);

  logger.info('混合认证验证成功', {
    authUser,
    isFixed,
    path: c.req.path,
  });

  return c.json({
    valid: true,
    user: authUser,
    authType: isFixed ? 'fixed_token' : 'jwt',
    message: '混合认证验证成功',
  });
});

/**
 * 获取当前认证用户信息
 */
router.get('/me', hybridAuth, async (c) => {
  const authUser = getCurrentAuthUser(c);
  const isFixed = isFixedTokenUser(c);

  if (!authUser) {
    return c.json({ error: 'Not authenticated' }, 401);
  }

  return c.json({
    user: authUser,
    authType: isFixed ? 'fixed_token' : 'jwt',
    isFixedToken: isFixed,
  });
});

/**
 * 测试管理员权限（固定令牌自动获得管理员权限）
 */
router.get('/admin-test', hybridAuth, async (c) => {
  const user = c.get('user');
  const authUser = getCurrentAuthUser(c);
  const isFixed = isFixedTokenUser(c);

  if (!user) {
    return c.json({ error: 'Not authenticated' }, 401);
  }

  // 检查是否有管理员权限
  const hasAdminRole = user.role === 'SYSTEM_ADMIN' || user.role === 'ORGANIZATION_ADMIN';

  return c.json({
    hasAdminRole,
    userRole: user.role,
    authType: isFixed ? 'fixed_token' : 'jwt',
    authUser,
    message: hasAdminRole ? '具有管理员权限' : '无管理员权限',
  });
});

/**
 * 健康检查（无需认证）
 */
router.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    service: 'access-token-test',
    timestamp: new Date().toISOString(),
    endpoints: {
      verify: 'POST /verify - 测试固定访问令牌认证',
      hybridVerify: 'POST /hybrid-verify - 测试混合认证',
      me: 'GET /me - 获取当前用户信息',
      adminTest: 'GET /admin-test - 测试管理员权限',
    },
    usage: {
      fixedToken: '使用 Access-Token 头或 Authorization: Bearer <固定令牌>',
      jwt: '使用 Authorization: Bearer <JWT令牌>',
    },
  });
});

export default router;
