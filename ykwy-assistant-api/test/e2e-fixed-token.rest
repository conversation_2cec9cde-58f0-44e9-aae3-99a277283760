### ykwy-assistant-api 固定访问令牌 E2E 测试

@baseUrl = http://localhost:3002
@salesToken = f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09
@adminToken = a1b2c3d4e5f6789012345678abcdef90123456789abcdef0123456789abcdef01
@invalidToken = invalid-token-123

### 健康检查
GET {{baseUrl}}/health

### 访问令牌测试服务
GET {{baseUrl}}/api/v1/access-token-test/health

### 固定令牌验证 - Access-Token
POST {{baseUrl}}/api/v1/access-token-test/verify
Access-Token: {{salesToken}}

### 固定令牌验证 - Authorization Bearer
POST {{baseUrl}}/api/v1/access-token-test/verify
Authorization: Bearer {{adminToken}}

### 获取当前用户信息
GET {{baseUrl}}/api/v1/access-token-test/me
Authorization: Bearer {{salesToken}}

### 管理员权限测试
GET {{baseUrl}}/api/v1/access-token-test/admin-test
Access-Token: {{adminToken}}

### 用户管理
GET {{baseUrl}}/api/v1/users
Access-Token: {{salesToken}}

### 团队管理
GET {{baseUrl}}/api/v1/teams
Authorization: Bearer {{adminToken}}

### 对话管理
GET {{baseUrl}}/api/v1/conversations
Access-Token: {{salesToken}}

### 消息管理
GET {{baseUrl}}/api/v1/messages
Authorization: Bearer {{adminToken}}

### 连接邀请
GET {{baseUrl}}/api/v1/connection-invitations
Access-Token: {{salesToken}}

### 千牛客户端
GET {{baseUrl}}/api/v1/qianniu-clients
Authorization: Bearer {{adminToken}}

### 千牛账号
GET {{baseUrl}}/api/v1/qianniu-accounts
Access-Token: {{salesToken}}

### 品牌管理
GET {{baseUrl}}/api/v1/brands
Authorization: Bearer {{adminToken}}

### 千牛TCP
GET {{baseUrl}}/api/v1/qianniu-tcp/status
Access-Token: {{salesToken}}

### 千牛API
GET {{baseUrl}}/api/v1/qianniu-api/status
Authorization: Bearer {{adminToken}}

### 平台WebSocket
GET {{baseUrl}}/api/v1/platform-ws/status
Access-Token: {{salesToken}}

### 销售智能体
GET {{baseUrl}}/api/v1/salesAgent
Authorization: Bearer {{adminToken}}

### 组织邀请
GET {{baseUrl}}/api/v1/organization-invitations
Access-Token: {{salesToken}}

### 连接诊断
GET {{baseUrl}}/api/v1/connection/diagnostics
Authorization: Bearer {{adminToken}}

### 错误处理测试

### 无效令牌 - Access-Token
GET {{baseUrl}}/api/v1/users
Access-Token: {{invalidToken}}

### 无效令牌 - Authorization Bearer
GET {{baseUrl}}/api/v1/brands
Authorization: Bearer {{invalidToken}}

### 缺少认证头
GET {{baseUrl}}/api/v1/teams

### 空令牌
GET {{baseUrl}}/api/v1/conversations
Access-Token:

### 错误的Authorization格式
GET {{baseUrl}}/api/v1/qianniu-clients
Authorization: InvalidFormat {{salesToken}}
