import { z<PERSON>alida<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { getCurrentUser } from '../lib/auth-utils';
import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { calculatePagination, createErrorResponse, createPaginatedResponse, createResponse, handleError } from '../lib/utils';
import { normalizeConversationCode } from '../lib/utils';
import { CreateMessageSchema, IdParamSchema, PaginationSchema } from '../lib/validations';
import { notifyConversationListChanged, notifyMessageListChanged } from '../services/eventService';
import { qianniuMessageService } from '../services/qianniuMessageService';

const messagesRoute = new Hono();

// 获取对话消息列表
messagesRoute.get(
  '/:conversationId/messages',
  zValidator(
    'param',
    z.object({
      conversationId: z.string().min(1, '对话ID不能为空'),
    }),
  ),
  zValidator('query', PaginationSchema),
  async (c) => {
    try {
      const { conversationId } = c.req.valid('param');
      const { page, limit } = c.req.valid('query');
      const { skip, take } = calculatePagination(page, limit);

      // 检查UUID格式避免Prisma验证错误，然后查找conversation
      let actualConversationId = conversationId;
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(conversationId);

      let conversationCheck = null;
      if (isUUID) {
        // 如果是UUID格式，通过ID查找
        conversationCheck = await prisma.conversation.findUnique({
          where: { id: conversationId },
          select: { id: true },
        });
      } else {
        // 如果不是UUID格式，通过conversationCode查找
        // 首先尝试精确匹配
        conversationCheck = await prisma.conversation.findFirst({
          where: {
            conversationCode: conversationId, // 精确匹配
            status: { not: 'CLOSED' },
          },
          select: { id: true },
        });

        // 如果精确匹配没找到，尝试前缀匹配（兼容性处理）
        if (!conversationCheck) {
          conversationCheck = await prisma.conversation.findFirst({
            where: {
              conversationCode: { startsWith: `${conversationId}#` }, // conversationCode + '#' 开头的匹配
              status: { not: 'CLOSED' },
            },
            select: { id: true },
          });

          // 如果找到前缀匹配，更新conversationCode为标准格式
          if (conversationCheck) {
            await prisma.conversation.update({
              where: { id: conversationCheck.id },
              data: {
                conversationCode: conversationId,
                lastActivity: new Date(),
              },
            });
          }
        }

        // 兼容性处理：如果conversationCode字段中没找到，尝试title字段（旧数据）
        if (!conversationCheck) {
          conversationCheck = await prisma.conversation.findFirst({
            where: {
              title: conversationId, // 精确匹配旧格式
              status: { not: 'CLOSED' },
            },
            select: { id: true },
          });

          // 如果在title中找到，迁移到conversationCode字段
          if (conversationCheck) {
            await prisma.conversation.update({
              where: { id: conversationCheck.id },
              data: {
                conversationCode: conversationId,
                title: null, // 清理旧字段
                lastActivity: new Date(),
              },
            });
          }
        }
      }

      if (conversationCheck) {
        actualConversationId = conversationCheck.id;
      }

      const [messages, total] = await Promise.all([
        prisma.message.findMany({
          where: {
            conversationId: actualConversationId,
            isRecalled: false, // 不返回已撤回的消息
          },
          skip,
          take,
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            parentMessage: {
              select: {
                id: true,
                content: true,
                senderType: true,
              },
            },
            replies: {
              select: {
                id: true,
                content: true,
                senderType: true,
                sentAt: true,
              },
              take: 3,
            },
          },
          orderBy: {
            sentAt: 'desc',
          },
        }),
        prisma.message.count({
          where: {
            conversationId: actualConversationId,
            isRecalled: false,
          },
        }),
      ]);

      return c.json(
        createPaginatedResponse(
          messages.reverse(), // 反转以按时间正序显示
          page,
          limit,
          total,
          '获取消息列表成功',
        ),
      );
    } catch (error) {
      return handleError(c, error);
    }
  },
);

// 发送消息
messagesRoute.post(
  '/:conversationId/messages',
  zValidator(
    'param',
    z.object({
      conversationId: z.string().min(1, '对话ID不能为空'),
    }),
  ),
  zValidator(
    'json',
    CreateMessageSchema.omit({ conversationId: true }).extend({
      senderId: z.string().uuid('无效的发送者ID').optional(),
      senderType: z.enum(['CUSTOMER', 'CUSTOMER_SERVICE', 'SYSTEM', 'AI']).default('CUSTOMER_SERVICE'),
    }),
  ),
  async (c) => {
    try {
      const { conversationId } = c.req.valid('param');
      const data = c.req.valid('json');

      // 获取当前用户
      const user = await getCurrentUser(c);
      if (!user) {
        return c.json(createErrorResponse('未登录'), 401);
      }

      const userId = user.id;

      // 获取对话以验证并获取 organizationId - 检查UUID格式避免Prisma验证错误
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(conversationId);
      let conversation = null;

      if (isUUID) {
        // 如果是UUID格式，通过ID查找
        conversation = await prisma.conversation.findUnique({
          where: { id: conversationId },
          select: { id: true, organizationId: true },
        });
      } else {
        // 如果不是UUID格式，标准化后通过conversationCode查找
        const normalizedCode = normalizeConversationCode(conversationId);
        
        if (!normalizedCode) {
          logger.warn('conversationId标准化失败', { originalId: conversationId });
          return c.json(createErrorResponse('无效的对话ID格式'), 400);
        }

        logger.debug('查找对话使用标准化conversationCode', {
          原始ID: conversationId,
          标准化Code: normalizedCode
        });

        // 首先尝试精确匹配标准化格式
        conversation = await prisma.conversation.findFirst({
          where: {
            conversationCode: normalizedCode, // 精确匹配标准化格式
            status: { not: 'CLOSED' },
          },
          select: { id: true, organizationId: true },
        });

        // 如果精确匹配没找到，尝试前缀匹配（兼容性处理）
        if (!conversation) {
          conversation = await prisma.conversation.findFirst({
            where: {
              conversationCode: { startsWith: `${normalizedCode}#` }, // conversationCode + '#' 开头的匹配
              status: { not: 'CLOSED' },
            },
            select: { id: true, organizationId: true },
          });

          // 如果找到前缀匹配，更新conversationCode为标准格式
          if (conversation) {
            logger.info('🔄 更新非标准格式conversationCode为标准格式', {
              conversationId: conversation.id,
              标准化Code: normalizedCode
            });
            
            await prisma.conversation.update({
              where: { id: conversation.id },
              data: {
                conversationCode: normalizedCode, // 更新为标准化格式
                lastActivity: new Date(),
              },
            });
          }
        }

        // 兼容性处理：如果conversationCode字段中没找到，尝试title字段（旧数据）
        if (!conversation) {
          conversation = await prisma.conversation.findFirst({
            where: {
              title: normalizedCode, // 使用标准化格式匹配旧格式
              status: { not: 'CLOSED' },
            },
            select: { id: true, organizationId: true },
          });

          // 如果在title中找到，迁移到conversationCode字段
          if (conversation) {
            logger.info('🔄 迁移旧格式title到标准化conversationCode', {
              conversationId: conversation.id,
              标准化Code: normalizedCode
            });
            
            await prisma.conversation.update({
              where: { id: conversation.id },
              data: {
                conversationCode: normalizedCode, // 使用标准化格式
                title: null, // 清理旧字段
                lastActivity: new Date(),
              },
            });
          }
        }
      }

      if (!conversation) {
        return c.json(createErrorResponse('对话不存在'), 404);
      }

      const actualConversationId = conversation.id;

      // 获取对话中的最新序列号
      const lastMessage = await prisma.message.findFirst({
        where: { conversationId: actualConversationId },
        orderBy: { sequenceNumber: 'desc' },
        select: { sequenceNumber: true },
      });

      const sequenceNumber = (lastMessage?.sequenceNumber || 0) + 1;

      const message = await prisma.$transaction(async (tx) => {
        // 创建消息
        const newMessage = await tx.message.create({
          data: {
            ...data,
            conversationId: actualConversationId,
            senderId: userId || data.senderId, // 使用当前用户ID
            metadata: data.metadata,
            sequenceNumber,
            sentAt: new Date(),
          },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            parentMessage: {
              select: {
                id: true,
                content: true,
                senderType: true,
              },
            },
          },
        });

        // 更新对话的最后活动时间
        await tx.conversation.update({
          where: { id: actualConversationId },
          data: { lastActivity: new Date() },
        });

        return newMessage;
      });

      // 触发WebSocket事件通知
      notifyMessageListChanged(conversation.organizationId, actualConversationId);
      notifyConversationListChanged(conversation.organizationId);

      // 如果是客服发送的消息，尝试转发到千牛客户端（使用广播功能）
      if (data.senderType === 'CUSTOMER_SERVICE') {
        try {
          // 检查当前用户是否有权限发送消息到这个对话
          const hasPermission = await checkUserSendPermission(user.id, actualConversationId);

          if (!hasPermission) {
            logger.warn('用户无权限发送消息到此对话，跳过千牛转发', {
              userId: user.id,
              conversationId: actualConversationId,
              reason: '用户未绑定对应的千牛连接',
            });
            // 不要中断主流程，只是跳过千牛转发
          } else {
            // 使用广播功能发送到相同conversationCode的所有对话
            const qianniuBroadcastSuccess = await qianniuMessageService.broadcastMessageToQianniu(actualConversationId, {
              content: data.content,
              messageType: data.messageType || 'text',
              metadata: data.metadata,
            });

            if (qianniuBroadcastSuccess) {
              logger.info('消息成功广播到千牛相关对话', { 
                conversationId: actualConversationId, 
                userId: user.id 
              });
            } else {
              logger.warn('消息广播到千牛失败', { 
                conversationId: actualConversationId, 
                userId: user.id 
              });
            }
          }
        } catch (error) {
          logger.error('广播消息到千牛时出错', { conversationId: actualConversationId, userId: user.id }, error instanceof Error ? error : new Error(String(error)));
          // 不影响主流程，只记录错误
        }
      }

      return c.json(createResponse(message, '发送消息成功'), 201);
    } catch (error) {
      return handleError(c, error);
    }
  },
);

// 撤回消息
messagesRoute.post(
  '/:conversationId/messages/:id/recall',
  zValidator('param', IdParamSchema),
  zValidator(
    'json',
    z.object({
      userId: z.string().uuid('无效的用户ID'),
      reason: z.string().optional(),
    }),
  ),
  async (c) => {
    try {
      const { id } = c.req.valid('param');
      const { userId, reason } = c.req.valid('json');

      // 检查消息是否存在且属于该用户
      const message = await prisma.message.findUnique({
        where: { id },
        include: {
          conversation: {
            // 包含 conversation 以获取 organizationId
            select: { organizationId: true, id: true },
          },
        },
      });

      if (!message || !message.conversation) {
        return c.json(createErrorResponse('消息或关联对话不存在'), 404);
      }

      if (message.isRecalled) {
        return c.json(createErrorResponse('消息已被撤回'), 400);
      }

      if (message.senderType !== 'CUSTOMER_SERVICE' || message.senderId !== userId) {
        return c.json(createErrorResponse('只能撤回自己发送的客服消息'), 403);
      }

      // 检查撤回时间限制（例如：5分钟内可撤回）
      const now = new Date();
      const sentTime = new Date(message.sentAt);
      const timeDiff = now.getTime() - sentTime.getTime();
      const maxRecallTime = 5 * 60 * 1000; // 5分钟

      if (timeDiff > maxRecallTime) {
        return c.json(createErrorResponse('超过撤回时间限制'), 400);
      }

      const updatedMessage = await prisma.message.update({
        where: { id },
        data: {
          isRecalled: true,
          recalledAt: new Date(),
          recalledBy: userId,
          recallReason: reason,
        },
      });

      // --- 触发WebSocket事件通知 ---
      const { organizationId, id: conversationId } = message.conversation;
      notifyMessageListChanged(organizationId, conversationId);
      notifyConversationListChanged(organizationId);
      // --- WebSocket事件通知结束 ---

      return c.json(createResponse(updatedMessage, '撤回消息成功'));
    } catch (error) {
      return handleError(c, error);
    }
  },
);

// 获取单个消息详情
messagesRoute.get('/:conversationId/messages/:id', zValidator('param', IdParamSchema), async (c) => {
  try {
    const { id } = c.req.valid('param');

    const message = await prisma.message.findUnique({
      where: { id },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        parentMessage: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        replies: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            sentAt: 'asc',
          },
        },
        conversation: {
          select: {
            id: true,
            title: true,
            customer: {
              select: {
                id: true,
                nickname: true,
                avatar: true,
              },
            },
          },
        },
      },
    });

    if (!message) {
      return c.json(createErrorResponse('消息不存在'), 404);
    }

    return c.json(createResponse(message, '获取消息详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

// 获取消息统计
messagesRoute.get('/stats', async (c) => {
  try {
    logger.info('开始获取消息统计');

    // 获取基础统计
    const [totalMessages, todayMessages, weekMessages, monthMessages] = await Promise.all([
      // 总消息数
      prisma.message.count({
        where: { isRecalled: false },
      }),
      // 今日消息数
      prisma.message.count({
        where: {
          isRecalled: false,
          sentAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
          },
        },
      }),
      // 本周消息数
      prisma.message.count({
        where: {
          isRecalled: false,
          sentAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      // 本月消息数
      prisma.message.count({
        where: {
          isRecalled: false,
          sentAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),
    ]);

    // 按发送者类型统计
    const messagesBySenderType = await prisma.message.groupBy({
      by: ['senderType'],
      where: { isRecalled: false },
      _count: {
        id: true,
      },
    });

    // 按消息类型统计
    const messagesByType = await prisma.message.groupBy({
      by: ['messageType'],
      where: { isRecalled: false },
      _count: {
        id: true,
      },
    });

    // 按小时统计今日消息分布
    const todayHourlyStats = (await prisma.$queryRaw`
      SELECT
        EXTRACT(HOUR FROM "sentAt") as hour,
        COUNT(*) as count
      FROM "message"
      WHERE "isRecalled" = false
        AND "sentAt" >= ${new Date(new Date().setHours(0, 0, 0, 0))}
      GROUP BY EXTRACT(HOUR FROM "sentAt")
      ORDER BY hour
    `) as Array<{ hour: number; count: bigint }>;

    const stats = {
      overview: {
        total: totalMessages,
        today: todayMessages,
        week: weekMessages,
        month: monthMessages,
      },
      bySenderType: messagesBySenderType.reduce(
        (acc, stat) => {
          acc[stat.senderType] = stat._count.id;
          return acc;
        },
        {} as Record<string, number>,
      ),
      byMessageType: messagesByType.reduce(
        (acc, stat) => {
          acc[stat.messageType] = stat._count.id;
          return acc;
        },
        {} as Record<string, number>,
      ),
      hourlyDistribution: todayHourlyStats.map((stat) => ({
        hour: Number(stat.hour),
        count: Number(stat.count),
      })),
    };

    logger.info('消息统计获取成功', { stats });

    return c.json(createResponse(stats, '获取消息统计成功'));
  } catch (error) {
    logger.error('获取消息统计失败', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * 检查用户是否有权限发送消息到指定对话
 * 权限规则：
 * - 组织管理员：可以发送所有消息
 * - 团队管理员：可以发送所属团队的消息
 * - 客服人员：只能发送绑定连接的消息
 */
async function checkUserSendPermission(userId: string, conversationId: string): Promise<boolean> {
  try {
    // 1. 获取用户信息和角色
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        role: true,
        teamId: true,
        members: {
          select: {
            role: true,
            organizationId: true,
          },
        },
      },
    });

    if (!user || !user.members.length) {
      return false;
    }

    const member = user.members[0];
    if (!member) {
      return false;
    }

    const memberRole = member.role || user.role;
    const organizationId = member.organizationId;

    // 2. 获取对话信息，包括关联的千牛账号和客户端
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        qianniuAccount: {
          include: {
            client: {
              select: {
                id: true,
                connectionId: true,
                organizationId: true,
              },
            },
          },
        },
      },
    });

    if (!conversation?.qianniuAccount?.client?.id) {
      // 如果对话没有关联千牛客户端，允许发送（可能是其他类型的对话）
      return true;
    }

    const qianniuClient = conversation.qianniuAccount.client;

    // 确保对话属于同一组织
    if (qianniuClient.organizationId !== organizationId) {
      return false;
    }

    // 3. 根据角色检查权限
    // 组织管理员：可以发送所有消息
    if (memberRole === 'ORGANIZATION_ADMIN') {
      return true;
    }

    // 4. 获取连接邀请信息（用于团队管理员和客服人员的权限检查）
    const connectionInvitation = await prisma.connectionInvitation.findFirst({
      where: {
        qianniuClientId: qianniuClient.id,
        status: 'ACTIVATED',
        organizationId: organizationId,
      },
      select: {
        teamId: true,
        boundUserId: true,
      },
    });

    if (!connectionInvitation) {
      return false;
    }

    // 团队管理员：可以发送所属团队的消息
    if (memberRole === 'TEAM_MANAGER' && user.teamId === connectionInvitation.teamId) {
      return true;
    }

    // 客服人员：只能发送绑定连接的消息
    if (memberRole === 'CUSTOMER_SERVICE' && user.id === connectionInvitation.boundUserId) {
      return true;
    }

    return false;
  } catch (error) {
    logger.error('检查用户发送权限失败', { userId, conversationId }, error instanceof Error ? error : new Error(String(error)));
    // 出错时默认拒绝权限
    return false;
  }
}

export default messagesRoute;
