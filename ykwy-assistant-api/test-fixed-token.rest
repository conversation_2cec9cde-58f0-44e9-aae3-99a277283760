### 测试固定访问令牌功能

### 1. 测试千牛API - 获取连接列表（使用 Access-Token 头）
GET http://localhost:3002/api/v1/qianniu-api/connections
Access-Token: f8e7d6c5b4a39281706f5*****281706f5e4d3c2b1a09
Content-Type: application/json

### 2. 测试千牛API - 获取连接列表（使用 Authorization Bearer）
GET http://localhost:3002/api/v1/qianniu-api/connections
Authorization: Bearer f8e7d6c5b4a39281706f5*****281706f5e4d3c2b1a09
Content-Type: application/json

### 3. 测试无效令牌（应该返回 401）
GET http://localhost:3002/api/v1/qianniu-api/connections
Access-Token: invalid-token
Content-Type: application/json

### 4. 测试没有令牌（应该返回 401）
GET http://localhost:3002/api/v1/qianniu-api/connections
Content-Type: application/json

### 5. 测试其他路由（应该需要JWT认证）
GET http://localhost:3002/api/v1/users/me
Access-Token: f8e7d6c5b4a39281706f5*****281706f5e4d3c2b1a09
Content-Type: application/json

### 6. 测试健康检查（无需认证）
GET http://localhost:3002/health
