### taobao-product-imformation E2E 测试

@baseUrl = http://localhost:8000
@shopId = 54898e9a-e7d2-4351-9f74-6e45d74ec153
@brandId = qinggong
@accessToken = f8e7d6c5b4a39281706f5e4d3c2b1a09e8f7d6c5b4a39281706f5e4d3c2b1a09
@invalidToken = invalid-token-123

### 健康检查
GET {{baseUrl}}/health

### 认证测试
POST {{baseUrl}}/auth/verify
Authorization: Bearer {{accessToken}}

### 数据同步 - 增量
POST {{baseUrl}}/sync
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "shop_id": "{{shopId}}",
  "incremental": true,
  "sync_products": true,
  "sync_qa": true
}

### 数据同步 - 全量
POST {{baseUrl}}/sync
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "shop_id": "{{shopId}}",
  "incremental": false,
  "sync_products": true,
  "sync_qa": true
}

### 只同步产品数据
POST {{baseUrl}}/sync/products?shop_id={{shopId}}&incremental=true
Authorization: Bearer {{accessToken}}

### 只同步问答数据
POST {{baseUrl}}/sync/qa?shop_id={{shopId}}&incremental=true
Authorization: Bearer {{accessToken}}

### 聊天测试
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "你好，有什么推荐的产品吗？",
  "brand_id": "{{brandId}}",
  "shop_id": "{{shopId}}"
}

### 向量搜索 - 产品
POST {{baseUrl}}/vector/search/products
Content-Type: application/json

{
  "query": "跑步外套",
  "limit": 3,
  "shop_id": "{{shopId}}"
}

### 向量搜索 - 问答知识库
POST {{baseUrl}}/vector/search/qa-knowledge
Content-Type: application/json

{
  "query": "产品保修政策",
  "brand_id": "{{brandId}}",
  "top_k": 5
}

### 智能问答
POST {{baseUrl}}/vector/intelligent-qa
Content-Type: application/json

{
  "question": "这个产品怎么样？",
  "brand_id": "{{brandId}}",
  "context": "用户正在咨询产品信息"
}

### 向量数据同步
POST {{baseUrl}}/vector/sync
Authorization: Bearer {{accessToken}}

### 重建索引
POST {{baseUrl}}/rebuild-index?brand_id={{brandId}}
Authorization: Bearer {{accessToken}}

### 获取当前用户信息
GET {{baseUrl}}/auth/me
Authorization: Bearer {{accessToken}}

### 获取品牌信息
GET {{baseUrl}}/brands

### 获取指定品牌信息
GET {{baseUrl}}/brands/{{brandId}}

### 向量统计信息
GET {{baseUrl}}/vector/statistics

### 向量健康检查
GET {{baseUrl}}/vector/health

### 错误处理测试

### 无效令牌
POST {{baseUrl}}/auth/verify
Authorization: Bearer {{invalidToken}}

### 缺少认证头
POST {{baseUrl}}/sync
Content-Type: application/json

{
  "shop_id": "{{shopId}}",
  "incremental": true,
  "sync_products": true,
  "sync_qa": true
}

